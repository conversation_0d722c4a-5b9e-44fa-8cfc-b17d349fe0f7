module.exports = {
    name: 'AccordionCard',
    publisher: '<PERSON>',
    group: 'OCAD University',
    cards: [{
        type: "AccordionCard",
        source: "./src/cards/AccordionCard",
        title: "AccordionCard",
        displayCardType: "Accordion",
        description: "Expandable categories with lists",
        template: {
            icon: "list-view",
            title: "Accordions",
            description: "Expandable categories with lists"
        },
        customConfiguration: {
            source: "./src/cards/AccordionCardConfig.jsx"
        }
    }],
    page: {
        source: './src/page/router.jsx'
    }
};