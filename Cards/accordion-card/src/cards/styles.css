#accordion-card button {
padding: 20px 20px;
margin-bottom: 0;
border-radius: 0px;
}

#accordion-card button svg {
flex: none !important;
margin-right: 15px;
height: 1.5rem;
}

#accordion-card button {
background-color: #1DB0F6;
justify-content: left;
font-family: "Gotham", <PERSON><PERSON>, sans-serif;
text-transform: none;
letter-spacing: 0px;
z-index: 9;
border-radius: 6px;
margin-bottom: 10px;
}


#accordion-card .accordion-item:nth-child(1n) button {
background-color: #1DB0F6 !important;
color: #fff;
}

#accordion-card .accordion-item:nth-child(2n) button{
background-color: #3CAB49 !important;
color: #fff;
}

#accordion-card .accordion-item:nth-child(3n) button{
background-color: #A014EC !important;
color: #fff;
}

#accordion-card .accordion-item:nth-child(4n) button {
background-color: #F7FE4F !important;
color: #000;
}

#accordion-card .accordion-item:nth-child(5n) button{
background-color: #F81423 !important;
color: #fff;
}

#accordion-card .accordion-item:nth-child(6n) button{
background-color: #086CF9 !important;
color: #fff;
}

#accordion-card .accordion-item:nth-child(7n) button{
background-color: #F26323 !important;
color: #fff;
}

#accordion-card .accordion-button::after {
filter: invert(1) saturate(0) brightness(10);
}


#accordion-card .accordion-item:nth-child(4n) button::after {
filter: invert(0) saturate(1) brightness(0) !important;
}    

#accordion-card .accordion-item {
    border: 0px !important;
}

#accordion-card .accordion-body a {
color: #000;
text-decoration: none;
border-bottom: 1px solid #000;
}

#accordion-card .accordion-body a:hover {
border-bottom: 3px solid #000;
}

#accordion-card .accordion-body ul {
padding-left: 1rem;
}

#accordion-card button:hover {
background-color: #000;
color: #fff;
border-color: #000;
}

#accordion-card .accordion-button:focus {
    box-shadow: none !important;
}

#accordion-card button:nth-child(2n){
background-color: #3CAB49 !important;
}

#accordion-card button:nth-child(3n){
background-color: #A014EC !important;
}

#accordion-card button:nth-child(4n){
background-color: #F7FE4F !important;
color: #000;
}

#accordion-card button:nth-child(5n){
background-color: #F81423 !important;
}

#accordion-card button:nth-child(6n){
background-color: #086CF9 !important;
}

#accordion-card button:nth-child(7n){
background-color: #F26323 !important;
}

#accordion-card .rich-text-content li {
list-style-type: none;
}

#accordion-card .accordion-body {
padding: 10px 5px;
}

#accordion-card .rich-text-content ul {
padding-left: 0px;
}

#accordion-card .rich-text-content li {
list-style-type: none;
margin-bottom: 9px;

}

#accordion-card .rich-text-content li:before {
content: "> ";
}

    
    


/* config styles */
table#button-list-config {
table-layout: fixed;
}

table#button-list-config td, table#button-list-config th {
max-width: 33%;
}
    
table#button-list-config td div, table#button-list-config th div  {
width: 100%;
}

button.add-button {
margin-top: 20px;
}

.ql-editor {
min-height: 300px;
}