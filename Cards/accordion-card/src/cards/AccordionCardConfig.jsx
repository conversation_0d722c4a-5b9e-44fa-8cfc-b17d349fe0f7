import React from "react";
import PropTypes from "prop-types";
import {
  <PERSON>Field,
  IconButton,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  RichTextEditor
} from "@ellucian/react-design-system/core";
import { Icon } from "@ellucian/ds-icons/lib";
const AccordionCardConfig = (props) => {
  const {
    cardControl: { setCustomConfiguration, setIsCustomConfigurationValid },
    cardInfo: {
      configuration: { customConfiguration }
    }
  } = props;

  // Move defaultLinks outside the component to prevent recreations
  const defaultLinks = React.useMemo(() => [{ label: "", icon: "bars", richText: "" }], []);
  
  // Initialize state with links from configuration or default
  const [links, setLinks] = React.useState(
    customConfiguration?.client?.links || defaultLinks
  );

  const saveConfiguration = React.useCallback((newLinks) => {
    if (!Array.isArray(newLinks)) {
      console.error('Invalid links format:', newLinks);
      return;
    }

    setCustomConfiguration({
      customConfiguration: {
        client: {
          links: newLinks
        },
        server: {}
      }
    });
    
    setIsCustomConfigurationValid(true);
  }, [setCustomConfiguration, setIsCustomConfigurationValid]);

  React.useEffect(() => {
    const clientLinks = customConfiguration?.client?.links;
    if (!clientLinks) {
      saveConfiguration(defaultLinks);
    }
  }, [customConfiguration, defaultLinks, saveConfiguration]);

  const handleChange = (i, e) => {
    const newLinks = [...links];
    newLinks[i][e.target.name] = e.target.value;
    setLinks(newLinks);
    saveConfiguration(newLinks);
  };

  const handleRichTextChange = (index, content, delta, source, editor) => {
    const htmlContent = editor.getHTML();
    const newLinks = [...links];
    newLinks[index].richText = htmlContent;
    setLinks(newLinks);
    saveConfiguration(newLinks);
  };

  const handleBlur = () => {
    setIsCustomConfigurationValid(true);
  };

  const addLink = () => {
    const newLinks = [...links, { label: "", icon: "", richText: "" }];
    setLinks(newLinks);
    saveConfiguration(newLinks);
  };

  const removeLink = (i) => {
    if (links.length <= 1) {
      return;
    }
    const newLinks = [...links];
    newLinks.splice(i, 1);
    setLinks(newLinks);
    saveConfiguration(newLinks);
  };

  return (
    <div>
      <Typography variant="h3">To find the names of icons available, please visit the <a href="https://fontawesome.com/search?ip=classic&s=solid&o=r"> Font Awesome Library</a></Typography>
      <Table id="button-list-config">
        <TableHead>
          <TableRow>
            <TableCell>Text</TableCell>
            <TableCell>Icon</TableCell>
            <TableCell>Action</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {links.map((element, index) => (
            <React.Fragment key={index}>
              <TableRow>
                <TableCell component="th" scope="row">
                  <TextField
                    label="Label"
                    margin="normal"
                    name="label"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.label || ''}
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    label="Icon"
                    margin="normal"
                    name="icon"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.icon || ''}
                  />
                </TableCell>
                <TableCell>
                  <IconButton
                    color="gray"
                    disabled={!index}
                    onClick={() => removeLink(index)}
                  >
                    <Icon color="red" name="trash" />
                  </IconButton>
                </TableCell>
              </TableRow>
              <TableRow className="richTextEditor-row">
                <TableCell colSpan="3"> 
                  <RichTextEditor 
                    id={`richTextEditor-${index}`}
                    value={element.richText || ''}
                    onChange={(content, delta, source, editor) => 
                      handleRichTextChange(index, content, delta, source, editor)
                    }
                  />
                </TableCell>
              </TableRow>
            </React.Fragment>
          ))}
        </TableBody>
      </Table>
      <IconButton className="add-button" onClick={addLink}>
        <Icon name="add" />
      </IconButton>
    </div>
  );
};

AccordionCardConfig.propTypes = {
  cardControl: PropTypes.object.isRequired,
  cardInfo: PropTypes.object.isRequired
};

export default AccordionCardConfig;
