<style>





* {
	font-family: Gotham, Helvetica Neue, Helvetica, -apple-system, BlinkMacSystemFont, Open Sans, Source Sans Pro, "Droid Serif", Arial, Segoe UI, sans-serif;
	transition: all 0.15s ease-out;
}

  /* Slider */
.slick-slider
{
    position: relative;

    display: block;
    box-sizing: border-box;

    -webkit-user-select: none;
       -moz-user-select: none;
        -ms-user-select: none;
            user-select: none;

    -webkit-touch-callout: none;
    -khtml-user-select: none;
    -ms-touch-action: pan-y;
        touch-action: pan-y;
    -webkit-tap-highlight-color: transparent;
}

.slick-list
{
    position: relative;

    display: block;
    overflow: hidden;

    margin: 0;
    padding: 0;
}	
.slick-list:focus
{
    outline: none;
}
.slick-list.dragging
{
    cursor: pointer;
    cursor: hand;
}

.slick-slider .slick-track,
.slick-slider .slick-list
{
    -webkit-transform: translate3d(0, 0, 0);
       -moz-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
         -o-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
}

.slick-track
{
    position: relative;
    top: 0;
    left: 0;

    display: block;
    margin-left: auto;
    margin-right: auto;
}
.slick-track:before,
.slick-track:after
{
    display: table;

    content: '';
}
.slick-track:after
{
    clear: both;
}
.slick-loading .slick-track
{
    visibility: hidden;
}

.slick-slide
{
    display: none;
    float: left;

    height: 100%;
    min-height: 1px;
}
[dir='rtl'] .slick-slide
{
    float: right;
}
.slick-slide img
{
    display: block;
}
.slick-slide.slick-loading img
{
    display: none;
}
.slick-slide.dragging img
{
    pointer-events: none;
}
.slick-initialized .slick-slide
{
    display: block;
}
.slick-loading .slick-slide
{
    visibility: hidden;
}
.slick-vertical .slick-slide
{
    display: block;

    height: auto;

    border: 1px solid transparent;
}
.slick-arrow.slick-hidden {
    display: none;
}



body, html {
padding: 0px;
margin: 0px;
height: 100%:
overflow-x: hidden;
}

.slick-slider {
z-index: 9;
}

.slick-slide > div {
width: 100% !important;
height: auto;
}

.slider-card {
font-family: "Roboto", sans-serif;
margin: 0px;
width: calc(100% - 8vw) !important;
height: calc(100vh - 70px);
padding: 5px 4vw 5px 4vw;
border-radius: 6px;
}

.slick-slider a {
color: #000;
}

.slider-card h3 {
font-size: 6vw;
margin: 0px 0px 15px 0px;
display: block;
}

.slider-card .date-raw {
font-size: 3.75vw;
margin-bottom: 0px;
display: block;
}



.slider-card img {
 display: none;
}

.card-container {
position: relative;
}

.controls {
position: fixed;
z-index: 99;
bottom: 10px;
right: 11px;
}

.controls button { 
background: #EFEFEF;
color: #000;
padding: 10px 25px 10px 25px;
border: 0px;
}

.controls button:first-of-type {
border-right: 1px solid #000;
border-radius: 0 0 0px 12px;
}


.controls button:nth-of-type(2) {
border-radius: 0 0 12px 0px;
}

.controls button:hover { 
color: #ffff;
background: #000;
}


.card-image {
border-radius: 6px;
width: 100%;
height: 40%;
float: left;
background-size: cover !important;
background-repeat: no-repeat !important;
background-position: center center !important;
margin-right: 4%;
}

.info-panel {
width: 100%;
height: 60%
white-space: nowrap;
overflow: hidden;
text-overflow: ellipsis;
margin-top: 2vw;
float: left;
overflow-y: scroll;
max-height: calc(60% + 10px);
}

.description {
font-size: 3.75vw;
display: block;
margin-bottom: 15px;
}

.date-raw {
font-size: 3.75vw;
font-style: italic;
padding-bottom: 20px;
}

.cta-banner {
position: fixed;
z-index: 99;
bottom: 0;
left: 0;
background: #000;
color: #fff;
height: 25px;
font-family: "Roboto", sans-serif;
font-size: 15px;
padding: 12px 40px 6px 10px;
border-radius: 0px 12px 0px 0px;
}

.cta-banner:hover {
color: #000;
background-color: #fff;
}

.card-image {position: relative;}
.employeeNotice .card-image:after {
content: "Employee Notice";
position: absolute;
top: 0px;
right: 0px;
background-color: #3CAB49;
color: #fff;
font-size: 10px;
padding: 10px;
border-radius: 0px 0px 0px 6px;
}

.studentNotice .card-image:after {
content: "Student Notice";
position: absolute;
top: 0px;
right: 0px;
background-color: #056CF9;
color: #fff;
font-size: 10px;
padding: 10px;
border-radius: 0px 0px 0px 6px;
}

</style>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,700;1,400&display=swap" rel="stylesheet">

<div class="card-container event-slider">
  <div class="slider">

  </div>
  <div class="controls"></div>
  
  <a href="https://ocaduniversity.sharepoint.com/sites/EmployeeNotices" target="_blank">
    <div class="cta-banner">See all Notices ⟶</div>
  </a>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-migrate/3.5.2/jquery-migrate.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.9.0/slick.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.30.1/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.7.8/handlebars.min.js" ></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.21/lodash.min.js"></script>



<script id="each-template" type="text/x-handlebars-template">
{{#each this}}
  <a href="{{this.webUrl}}" class="main-link" target="_blank">
    <div class="slider-card">
      <div class="card-image" data-image="{{this.webParts.0.data.serverProcessedContent.imageSources.0.value}}"></div>
      <div class="info-panel">
        <h3 class="title">{{this.title}}</h3>
        <span class="description">{{this.description}}</span>
        <span class="date-raw"><span class="formatted-date" data-date="{{{this.createdDateTime}}}"></span></span>
      </div>
    </div>
  </a>
{{/each}}
</script>


<script>
  
  
var template;
var students;
var employees;

$.ajax({
url: 'https://search.ocadu.ca/announcements/student/5',
type: "GET",
dataType: "json",
success: function (data) {   
	students = data;

	$.ajax({
	url: 'https://search.ocadu.ca/announcements/employee/5',
	type: "GET",
	dataType: "json",
	success: function (data) {   
	employees = data;

	data = $.merge(students, employees);

	data = _.sortBy(data, "createdDateTime").reverse();

	  template = Handlebars.compile($("#each-template").html());
	  $('.slider').html(template(data));
  
	  $('.slider').slick({
		appendArrows: $('.controls'),
		nextArrow: '<button type="button" class="slick-next">►</button>',
		prevArrow: '<button type="button" class="slick-prev">◄</button>'
	  });
  
	  $(".formatted-date" ).each(function( index ) {
		var date = new Date($(this).attr("data-date"));
		$(this).html(moment(date).format("MMMM Do YYYY"));
	  });
  
	  $(".card-image" ).each(function( index ) {
	  
		let imageSource = $(this).attr("data-image");
		if (imageSource.includes("sites")) {
			imageSource = "https://ocaduniversity.sharepoint.com/" + imageSource;
		}
		
		$(this).css('background-image', 'url(' + imageSource + ')');
		$(this).css({
			'background-image' : 'url(' + imageSource + '), url(https://live-ocad-dpl.pantheonsite.io/sites/default/files/2024-09/placeholder.jpg)'
		});
	  });
  
  
	  $(".main-link").each(function( index ) {
		let audience = $(this).attr('href');
		if (audience.includes("EmployeeNotices")) {
			$(this).addClass("employeeNotice");
		}
		if (audience.includes("StudentNotices")) {
			$(this).addClass("studentNotice");
		}
	  });

	}
	});


}
});




 
</script>

