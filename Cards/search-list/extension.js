module.exports = {
    name: 'SearchList',
    publisher: '<PERSON>',
    cards: [{
        type: 'SearchList',
        source: './src/cards/SearchList',
        title: 'Searchable List Card',
        displayCardType: 'Searchable List Card',
        description: 'Card with searchable list of items',
        template: {
            icon: "search",
            title: "Searchable List",
            description: "A searchable list of configurable items"
        },
        customConfiguration: {
            source: "./src/cards/SearchListTemplateConfig.jsx"
        }
    }],
    page: {
        source: './src/page/router.jsx'
    }
};
