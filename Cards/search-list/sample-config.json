{"items": [{"title": "Student Portal", "description": "Access your student information and resources", "url": "https://student.example.edu", "icon": "graduation-cap", "keywords": "grades transcript registration enrollment classes schedule"}, {"title": "Library Resources", "description": "Search the library catalog and access online resources", "url": "https://library.example.edu", "icon": "book", "keywords": "books journals articles research databases ebooks"}, {"title": "Campus Map", "description": "Interactive map of campus buildings and facilities", "url": "https://map.example.edu", "icon": "map-marker-alt", "keywords": "directions buildings classrooms offices parking navigation"}]}