import React from "react";
import PropTypes from "prop-types";
import {
  <PERSON><PERSON>ield,
  IconButton,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Button
} from "@ellucian/react-design-system/core";
import { Icon } from "@ellucian/ds-icons/lib";
import "./styles.css";

const SearchListTemplateConfig = (props) => {
  const {
    cardControl: { setCustomConfiguration, setIsCustomConfigurationValid },
    cardInfo: {
      configuration: { customConfiguration }
    }
  } = props;

  const client = customConfiguration ? customConfiguration.client : undefined;
  const [items, setItems] = React.useState(
    client ? client.items : [{ title: "", description: "", url: "", icon: "", keywords: "", audience: "", category: "" }]
  );
  const [iconColor, setIconColor] = React.useState(
    client ? client.iconColor : "#056CF9"
  );

  const colorOptions = [
    { label: "White", value: "#ffffff" },
    { label: "Black", value: "#000000" },
    { label: "Red", value: "#F81423" },
    { label: "Orange", value: "#F26323" },
    { label: "Yellow", value: "#F6FE4F" },
    { label: "Green", value: "#3CAB49" },
    { label: "Light Blue", value: "#1AB0F6" },
    { label: "Blue", value: "#056CF9" },
    { label: "Purple", value: "#A014EC" }
  ];

  const handleChange = (i, e) => {
    const newItems = [...items];
    newItems[i][e.target.name] = e.target.value;
    setItems(newItems);

    setCustomConfiguration({
      customConfiguration: {
        client: {
          items: newItems,
          iconColor: iconColor
        }
      }
    });
  };

  const handleColorChange = (e) => {
    const newColor = e.target.value;
    setIconColor(newColor);

    setCustomConfiguration({
      customConfiguration: {
        client: {
          items: items,
          iconColor: newColor
        }
      }
    });
  };

  const addItem = () => {
    setItems([...items, { title: "", description: "", url: "", icon: "", keywords: "", audience: "", category: "" }]);
  };

  const removeItem = (i) => {
    const newItems = [...items];
    newItems.splice(i, 1);
    setItems(newItems);

    setCustomConfiguration({
      customConfiguration: {
        client: {
          items: newItems,
          iconColor: iconColor
        }
      }
    });
  };

  const handleBlur = (e) => {
    setIsCustomConfigurationValid(e.target.value !== "");
  };

  const exportData = async () => {
    // Create a formatted string with all the list items data
    const exportText = items.map((item, index) => {
      return `Item ${index + 1}:
Title: ${item.title || ''}
Description: ${item.description || ''}
URL: ${item.url || ''}
Icon: ${item.icon || ''}
Keywords: ${item.keywords || ''}
Audience: ${item.audience || ''}
Category: ${item.category || ''}
Icon Color: ${iconColor}
---`;
    }).join('\n\n');

    try {
      // Copy to clipboard
      await navigator.clipboard.writeText(exportText);

      // You could add a success notification here if needed
      console.log('Data exported to clipboard successfully');
    } catch (err) {
      console.error('Failed to copy data to clipboard:', err);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = exportText;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
    }
  };

  return (
    <div>
      <Typography variant="h3">Configure list items that will be searchable</Typography>
      <Typography variant="body2" style={{ marginBottom: '16px' }}>
        To find the names of icons available, please visit the <a href="https://fontawesome.com/search?ip=classic&s=solid&o=r" target="_blank" rel="noopener noreferrer">Font Awesome Library</a>
      </Typography>

      <div style={{ marginBottom: '32px' }}>
        <Typography variant="h4" style={{ marginBottom: '16px', fontWeight: 600 }}>
          Additional Configuration
        </Typography>

        <div style={{ display: 'flex', gap: '32px', alignItems: 'flex-start', flexWrap: 'wrap' }}>
          {/* Export Data Section */}
          <div style={{ flex: '1', minWidth: '250px' }}>
            <Typography variant="body1" style={{ marginBottom: '8px', fontWeight: 500 }}>
              Export Data
            </Typography>
            <Button
              onClick={exportData}
              variant="contained"
              style={{
                backgroundColor: '#056CF9',
                color: 'white',
                marginBottom: '8px'
              }}
            >
              Export Data
            </Button>
            <Typography variant="body2" style={{ color: '#666', fontSize: '12px' }}>
              Copy all list items to clipboard
            </Typography>
          </div>

          {/* Icon Color Section */}
          <div style={{ flex: '1', minWidth: '250px' }}>
            <Typography variant="body1" style={{ marginBottom: '8px', fontWeight: 500 }}>
              Icon Color
            </Typography>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <select
                value={iconColor}
                onChange={handleColorChange}
                style={{
                  padding: '8px 12px',
                  borderRadius: '4px',
                  fontSize: '14px',
                  border: '1px solid #b2b3b7',
                  backgroundColor: 'white',
                  minWidth: '200px',
                  height: '40px'
                }}
              >
                {colorOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <div style={{
                width: '20px',
                height: '20px',
                backgroundColor: iconColor,
                border: '1px solid #ccc',
                borderRadius: '3px'
              }} />
            </div>
          </div>
        </div>
      </div>
      <Table id="search-list-config">
        <TableHead>
          <TableRow>
            <TableCell>Title</TableCell>
            <TableCell>Description</TableCell>
            <TableCell>URL</TableCell>
            <TableCell>Icon</TableCell>
            <TableCell>Action</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {items.map((element, index) => (
            <React.Fragment key={index}>
              <TableRow>
                <TableCell component="th" scope="row">
                  <TextField
                    label="Title"
                    margin="normal"
                    name="title"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.title}
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    label="Description"
                    margin="normal"
                    name="description"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.description}
                    inputProps={{ maxLength: 120 }}
                    helperText={`${(element.description || '').length}/120 characters`}
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    label="URL"
                    margin="normal"
                    name="url"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.url}
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    label="Icon"
                    margin="normal"
                    name="icon"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.icon}
                  />
                </TableCell>
                <TableCell>
                  <IconButton
                    color="gray"
                    disabled={!index}
                    onClick={() => removeItem(index)}
                  >
                    <Icon color="red" name="trash" />
                  </IconButton>
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell>
                  <TextField
                    label="Keywords"
                    margin="normal"
                    name="keywords"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.keywords || ''}
                    helperText="Hidden searchable keywords"
                    fullWidth
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    label="Audience"
                    margin="normal"
                    name="audience"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.audience || ''}
                    helperText="Comma-separated roles (leave empty for all)"
                    fullWidth
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    label="Category"
                    margin="normal"
                    name="category"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.category || ''}
                    helperText="Group items by category"
                    fullWidth
                  />
                </TableCell>
                <TableCell colSpan={2}></TableCell>
              </TableRow>
            </React.Fragment>
          ))}
        </TableBody>
      </Table>
      <IconButton className="add-button" onClick={() => addItem()}>
        <Icon name="add" />
      </IconButton>
    </div>
  );
};

SearchListTemplateConfig.propTypes = {
  cardControl: PropTypes.object,
  cardInfo: PropTypes.object
};

export default SearchListTemplateConfig;
