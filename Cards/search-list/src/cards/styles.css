/* global stuff */


@import url("https://use.typekit.net/thk7oyq.css");

.eee-dashboard-card *  {
font-family: "Gotham", Arial, sans-serif !important;
transition: all 0.15s ease-out;
letter-spacing: 0px !important;
}

#search-list-config th div, #search-list-config td div {
width: 100%;
}

/* Add these styles for the two-row layout */
#search-list-config tr:nth-child(even) {
  background-color: #f9f9f9;
}

#search-list-config tr:nth-child(odd) + tr:nth-child(even) {
  border-bottom: 1px solid #e0e0e0;
}

#search-list-config td {
  vertical-align: top;
}

button.add-button {
  margin-top: 20px;
}

/* Drag and drop styles */
#search-list-config tr[draggable="true"]:hover {
  background-color: #f0f8ff;
  transition: background-color 0.2s ease;
}

#search-list-config tr[draggable="true"]:active {
  opacity: 0.7;
}

#search-list-config .drag-handle {
  cursor: grab;
  user-select: none;
}

#search-list-config .drag-handle:active {
  cursor: grabbing;
}
