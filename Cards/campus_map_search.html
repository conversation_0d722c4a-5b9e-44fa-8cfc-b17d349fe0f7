<style>


* {
	font-family: Gotham, Helvetica Neue, Helvetica, -apple-system, BlinkMacSystemFont, Open Sans, Source Sans Pro, "Droid Serif", Aria<PERSON>, Segoe UI, sans-serif;
	transition: all 0.15s ease-out;
}

body,
html {
	padding: 0px;
	margin: 0px;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center center;
	height: 98vh;
	overflow-x: hidden;
}

.map-container {
	width: 92vw;
	padding: 10px 4vw 0px 4vw;
	position: static;
	left: 0;
	top: 0%;
	z-index: 999;
	background: transparent
}

.map-box {
	width: 80%;
height: 40px;
	font-size: clamp(20px, 6vw, 24px);

	border: 1px solid #888;
	padding: 2vw;
	border-radius: 6px 0px 0px 6px;
	margin-bottom: 4vw;
	background: #fff;
	z-index: 10;
	float: left;
	font-size: 5vw;
}

.map-btn {
	width: 14%;
height: 38px;
	float: left;
	font-size: 5vw;
	border: 0px;
	padding: 0vw 2vw 0vw 2vw;
	border-radius: 0px 6px 6px 0px;
	margin-bottom: 2vw;
	background-color: #1ab0f6;
	color: #fff;
	text-align: center;
	min-height: 0px;
	z-index: 10;
	border: 1px solid #1ab0f6;
	text-decoration: none;
	line-height: 38px;
}


.map-btn:hover {
	color: #fff;
	background: #000;
	border: 1px solid #000;
}

.map-btn:hover::after {
	color: #000;
}

.pattern svg {
	width: 146vw;
	position: fixed;
	left: 0px;
	bottom: -2.5vw;
	z-index: 9;
}

h1 {
	color: #fff;
	font-size: 7vw;
	text-align: center;
	color: #000;
}

  .footer-button {
    border-radius: 6px;
    width: 80%;
    background: #000;
    color: #fff;
    font-size: 4vw;
    padding: 15px;
    position: absolute;
    bottom: 10px;
    left: 10%;
    text-decoration: none;
    text-align: center;
    box-sizing: border-box;
  }
  
    .footer-button:hover {
    background: #1AB0F6;
    }
</style>

<body style="background-image: linear-gradient( rgba(255,255,255,.5), rgba(255,255,255,.5) ), url('https://live-ocad-dpl.pantheonsite.io/sites/default/files/myOCADU/images/map.jpg');">

<form>
<div class="map-container">
	<h1>Find Location</h1>
	<input class="map-box" placeholder="Search for location...">
	<div class="map-btn" href="#">→</div>
</div>
</form>

	<a href="https://map.ocadu.ca/" class="footer-button" target="_blank">View Full Map</a>

</body>

  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

<script>
  $('.map-btn').on('click', function(e) {
  
  let a= document.createElement('a');
  let response = 'https://map.ocadu.ca/?id=1998#!ct/60994,60995,60996,60997,60998,61000,61001,61002,61003,61004,61005,61006,61138,61139,61140,61141,61142,61143,61144,61146,61147,61148,61149,61150,61151,61152,61153,61154,61155,61156,61157,61159,61160,61161,61162,61163,61165,61166,61167,61168,61169,61170,61174,61175,61176,61177,61178,61179,61181,61182,61183,61184,61185,61186,61187,61188,61189,61190,61191,61192,61193,61194,61195,61196,61197,61198,61199,61200?s/' + $(".map-box").val();
	a.target= '_blank';
	a.href= response;
	a.click();
});

$('.map-box').keypress(function (e) {
   var keyCode = e.keyCode ? e.keyCode : e.which;       
   if (keyCode == 13) 
   {
      e.preventDefault();
  let a= document.createElement('a');
  let response = 'https://map.ocadu.ca/?id=1998#!ct/60994,60995,60996,60997,60998,61000,61001,61002,61003,61004,61005,61006,61138,61139,61140,61141,61142,61143,61144,61146,61147,61148,61149,61150,61151,61152,61153,61154,61155,61156,61157,61159,61160,61161,61162,61163,61165,61166,61167,61168,61169,61170,61174,61175,61176,61177,61178,61179,61181,61182,61183,61184,61185,61186,61187,61188,61189,61190,61191,61192,61193,61194,61195,61196,61197,61198,61199,61200?s/' + $(".map-box").val();
	a.target= '_blank';
	a.href= response;
	a.click();
      return false; 
   }
});
  </script>