/* global stuff */


@import url("https://use.typekit.net/thk7oyq.css");

.eee-dashboard-card *  {
font-family: "Gotham", Arial, sans-serif !important;
transition: all 0.15s ease-out;
letter-spacing: 0px !important;
}

:root {
    --ocad-blue: #086CF9;
    --ocad-light-blue: #1AB0F6;
    --ocad-purple: #A014EC;
    --ocad-orange: bro##F26323;
    --ocad-red: #F81423;
    --ocad-green: #3CAB49;
    --ocad-yellow: #F7FE4F;
  }


/* super search */

.super-search {
background: #000;
width: 100vw;
padding: 20px 60px 20px 60px;
background-color: var(--ocad-yellow);
margin-top: -1px;
}


.super-search .search-box {
width: 80%;
border: 1px solid #fff;
padding: 10px;
border-radius: 6px 0px 0px 6px;
background: #fff;
z-index: 10;
font-size: 1.0rem;
display: inline-block;
}

.super-search .search-btn {
width: 20%;
display: inline-block;
font-size: 1.5rem;
border: 0px;
padding: 0px 0px 0px 0px;
border-radius: 0px 6px 6px 0px;
background-color: #000;
color: #fff;
text-align: center;
min-height: 0px;
z-index: 10;
border: 1px solid #000;
text-decoration: none;
line-height: 38.5px;
position: relative;
top: 3px;
}


.super-search .search-btn:hover {
color: #000;
background-color: var(--ocad-yellow);
border: 1px solid #000;
}

.super-search .search-btn:hover::after {
color: #000;
}

@media (max-width: 768px) {
.super-search {
padding: 23px 24px 16px 24px;
}
}



/* search card */

.search-container {
    width: 100%;
    height: 100%;
    background-image: url('https://live-ocad-dpl.pantheonsite.io/sites/default/files/myOCADU/images/wave.svg');
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: 50% 100%;
    padding: 20px;
    background-color: #000;
    }
    
    .search-container input {
    width: 80%;
    border: 1px solid #fff;
    padding: 10px;
    border-radius: 6px 0px 0px 6px;
    background: #fff;
    z-index: 10;
    font-size: 1.0rem;
    display: inline-block;
    }
    
    .search-container .search-btn {
    width: 20%;
    display: inline-block;
    font-size: 1.5rem;
    border: 0px;
    padding: 5px 0px 15px 0px;
    border-radius: 0px 6px 6px 0px;
    background-color: var(--ocad-light-blue);
    color: #fff;
    text-align: center;
    min-height: 0px;
    z-index: 10;
    border: 1px solid var(--ocad-light-blue);
    text-decoration: none;
    line-height: 23.5px;
    position: relative;
    top: -2px;
    }
    
    .search-container .search-btn:hover {
    color: #fff;
    background: #000;
    border: 1px solid #fff;
    }
    
    .search-container h1 {
    margin-top: 15%;
    font-size: 1.6rem;
    text-align: center;
    font-weight: 800;
    color: #fff;
    }