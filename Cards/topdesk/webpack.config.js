const webpack = require('webpack');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables from .env file
const env = dotenv.config().parsed || {};

// Create a new webpack.DefinePlugin that replaces 'process.env.X' with the actual value
const envKeys = Object.keys(env).reduce((prev, next) => {
  prev[`process.env.${next}`] = JSON.stringify(env[next]);
  return prev;
}, {});

// Add default values for required environment variables
const defaultEnv = {
  'process.env.TOPDESK_API_URL': JSON.stringify('https://ocadu.topdesk.net/tas/api'),
  'process.env.TOPDESK_API_USERNAME': JSON.stringify(''),
  'process.env.TOPDESK_API_PASSWORD': JSON.stringify('')
};

// Merge default values with actual environment variables
const definePluginConfig = { ...defaultEnv, ...envKeys };

const packageJson = require('./package.json');
const extensionConfig = require('./extension.js');
 
const { webpackConfigBuilder } = require('@ellucian/experience-extension');

module.exports = async (env, options) => {

    // Generate Webpack configuration based on the extension.js file
    // and any optional env flags  ("--env verbose", "--env upload", etc)
    const webpackConfig = await webpackConfigBuilder({
        extensionConfig: extensionConfig,
        extensionVersion: packageJson.version,
        mode: options.mode || 'production',
        verbose: env.verbose || process.env.EXPERIENCE_EXTENSION_VERBOSE || false,
        upload: env.upload || process.env.EXPERIENCE_EXTENSION_UPLOAD || false,
        forceUpload: env.forceUpload || process.env.EXPERIENCE_EXTENSION_FORCE_UPLOAD || false,
        uploadToken: process.env.EXPERIENCE_EXTENSION_UPLOAD_TOKEN,
        liveReload: env.liveReload || false,
        port: process.env.PORT || 8082
    });

    // For advanced scenarios, dynamically modify webpackConfig here.

    return webpackConfig;
};
