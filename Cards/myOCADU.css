* {
	font-family: Gotham, Helvetica Neue, Helvetica, -apple-system, BlinkMacSystemFont, Open Sans, Source Sans Pro, "Droid Serif", Arial, Segoe UI, sans-serif;
	transition: all 0.15s ease-out;
	box-sizing: border-box;
}

/* callout card and service card */

body, html {
  width: 100vw;
  overflow-x: hidden;
  padding: 0px;
  margin: 0px;
}

.service-wrapper img {
  width: 30%;
  margin-left: 35%;
}

.service-wrapper a:visited {
	color: #000;
}

.service-wrapper li a {
	color: #000;
	text-decoration: none;
	border-bottom: 2px solid #000;
}

.service-wrapper li a:hover {
	color: #000;
	border-bottom: 2px solid #979797;
}

  
.service-wrapper li {
margin: 0px 0px 7px 0px;
font-size: 4vw;
}

.service-wrapper {
 padding: 4vw;
}

.service-wrapper .icon {
width: 100%;
margin: 0px;
padding: 0px;
}

.footer-button {
border-radius: 6px;
width: 80%;
background: #000;
color: #fff;
font-size: 4vw;
padding: 15px;
position: absolute;
bottom: 10px;
left: 10%;
text-decoration: none;
text-align: center;
box-sizing: border-box;
}

.footer-button:hover {
background: #1AB0F6;
}

.footer-button.light {
border-radius: 6px;
width: 80%;
background: #fff;
color: #000;
font-size: 4vw;
padding: 15px;
position: absolute;
bottom: 10px;
left: 10%;
text-decoration: none;
text-align: center;
box-sizing: border-box;
}

.footer-button.light:hover {
background: #1AB0F6;
}

a.footer-button::after {
font-family: arial !important;
content: "\2192";
margin-left: 10px;
}


a.footer-button:hover::after {
font-family: arial !important;
content: "\2192";
margin-left: 20px;
}

   
    
.service-wrapper a::after {
font-family: arial !important;
content: "\2192";
margin-left: 10px;
}


.service-wrapper a:hover::after {
font-family: arial !important;
content: "\2192";
margin-left: 20px;
}




/* background callout card */


body.service-callout-body {
	-webkit-box-shadow: inset 0 0 130px #000;
	-moz-box-shadow: inset 0 0 30px #000;
	box-shadow: inset 0 0 130px #000;
	padding: 20px;
	background-size: cover;
	background-position: center center;
    background-blend-mode: multiply;
    background-repeat: no-repeat;
    height: 100vh;
}

.background-card-wrapper {
	padding: 4vw;
	position: fixed;
	left: 0;
	top: 50%;
    transform: translateY(-50%);
    padding-bottom: 50px;
    width: 100%;
}

.background-card-wrapper a.button {
	border-radius: 6px;
	width: 80%;
	background: #fff;
	color: #000 !important;
	font-size: 4vw;
	padding: 15px;
	position: absolute;
	bottom: 10px;
	left: 10%;
	text-decoration: none;
	text-align: center;
}

.background-card-wrapper a.button:hover {
	background: #1AB0F6;
	border: 0px !important;
}

.background-card-wrapper a.button::after {
	content: "→";
	margin-left: 10px;
}

.background-card-wrapper a.button:hover::after {
	content: "→";
	margin-left: 20px;
}

.background-card-wrapper h1 {
	color: #fff;
	font-size: 7vw;
	text-align: center;
}

.background-card-wrapper h2 {
	color: #fff;
	text-align: center;
	font-weight: 400;
	font-size: 4.5vw;
}

.background-card-wrapper a {
	color: #fff;
	text-decoration: none;
	border-bottom: 2px solid #fff;
}

.background-card-wrapper a:hover {
	color: #fff;
	text-decoration: none;
	border-bottom: 2px solid #979797;
}

.background-card-wrapper a:visited,
{
color: #fff;
}