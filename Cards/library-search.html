<style>

* {
	font-family: Gotham, Helvetica Neue, Helvetica, -apple-system, BlinkMacSystemFont, Open Sans, Source Sans Pro, "Droid Serif", Aria<PERSON>, Segoe UI, sans-serif;
	transition: all 0.15s ease-out;
}


body,
html {
	padding: 0px 0px 0px 0px;
	margin: 0px;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center center;
	height: 99vh;
	overflow-x: hidden;
}


.map-container {
	width: 92vw;
	padding: 10px 4vw 0px 4vw;
	position: static;
	left: 0;
	top: 0%;
	z-index: 999;
	background: transparent
}

.map-box {
	width: 80%;
height: 40px;
	font-size: clamp(20px, 6vw, 24px);

	border: 1px solid #888;
	padding: 2vw;
	border-radius: 6px 0px 0px 6px;
	margin-bottom: 4vw;
	background: #fff;
	z-index: 10;
	float: left;
	font-size: 5vw;
}

.map-btn {
	width: 14%;
height: 38px;
	float: left;
	font-size: 5vw;
	border: 0px;
	padding: 0vw 2vw 0vw 2vw;
	border-radius: 0px 6px 6px 0px;
	margin-bottom: 2vw;
	background-color: #1ab0f6;
	color: #fff;
	text-align: center;
	min-height: 0px;
	z-index: 10;
	border: 1px solid #1ab0f6;
	text-decoration: none;
	line-height: 38px;
}


.map-btn:hover {
	color: #fff;
	background: #000;
	border: 1px solid #000;
}

.map-btn:hover::after {
	color: #000;
}

.pattern svg {
	width: 146vw;
	position: fixed;
	left: 0px;
	bottom: -2.5vw;
	z-index: 9;
}

h1 {
	color: #fff;
	font-size: 7vw;
	text-align: center;
	color: #000;
	margin: 10px 0px;
}

 
 .button-container {
clear: both;
max-width: 90%;
width: 100%;
margin-left: 5%;
margin-top: 5vw;
display: flex;
flex-wrap: wrap;
}

 .footer-button {
 flex-basis: 48%;
  box-sizing: border-box;
    width: 0;
    border-radius: 6px;
    background: #000;
    color: #fff;
    font-size: 4vw;
    padding: 15px;
    float: left;
    text-decoration: none;
    text-align: center;
    box-sizing: border-box;
margin-bottom: 4%;
vertical-align: center;
  }

 .footer-button:nth-child(odd) {
    margin-right: 4%;
}



.footer-button:nth-child(1n):hover {
background: #5da955;
}

.footer-button:nth-child(2n):hover {
background: #53adf0;
color: #000;
}

.footer-button:nth-child(2n):hover img {
filter: invert(1);
}

.footer-button:nth-child(3n):hover {
background: #9325e3;
}

.footer-button:nth-child(4n):hover {
background: #2f6af0;
}

.footer-button:nth-child(5n):hover {
background: #e43732;
}

.footer-button:nth-child(6n):hover {
background: #e16c38;
}

.footer-button:nth-child(7n):hover {
background: #f3e65b;
color: #000;
}

</style>

<body style="background-image: linear-gradient( rgba(255,255,255,.5), rgba(255,255,255,.5) ), url('https://www.ocadu.ca/sites/default/files/myOCADU/images/library.jpg');">
<br>
<form>
<div class="map-container">
	<input class="map-box" placeholder="Enter keywords or topics">
	<div class="map-btn" href="#">→</div>
</div>
</form>
<div class="button-container">
	<a href="https://ocadu.primo.exlibrisgroup.com/discovery/search?vid=01OCUL_OCAD:OMNI&mode=advanced" class="footer-button" target="_blank">Advanced Search</a>
	<a href="https://ocadu.primo.exlibrisgroup.com/discovery/jsearch?vid=01OCUL_OCAD:OMNI" class="footer-button" target="_blank">Journal Search</a>
	<a href="https://ocadu.libguides.com/az.php" class="footer-button" target="_blank">Databases A-Z</a>
	<a href="http://ocadu.libguides.com/index.php?b=s" class="footer-button" target="_blank">Research Guides</a>
	<a href="https://ocadu.alma.exlibrisgroup.com/leganto/login?auth=SAML" class="footer-button" target="_blank">Course Reserves</a>
	<a href="https://ocadu.primo.exlibrisgroup.com/discovery/login?vid=01OCUL_OCAD:OMNI" class="footer-button" target="_blank">My Account</a>
</div>
</body>

  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

<script>
  $('.map-btn').on('click', function(e) {
  
  let a= document.createElement('a');
  let response = 'https://ocadu.primo.exlibrisgroup.com/discovery/search?vid=01OCUL_OCAD:OMNI&tab=OCULDiscoveryNetwork&mode=basic&displayMode=full&bulkSize=10&highlight=true&dum=true&query=any,contains,' + $(".map-box").val() + '&displayField=all&search_scope=OCULDiscoveryNetwork&pcAvailabiltyMode=true';
	a.target= '_blank';
	a.href= response;
	a.click();
});

$('.map-box').keypress(function (e) {
   var keyCode = e.keyCode ? e.keyCode : e.which;       
   if (keyCode == 13) 
   {
      e.preventDefault();
  let a= document.createElement('a');
  let response = 'https://ocadu.primo.exlibrisgroup.com/discovery/search?vid=01OCUL_OCAD:OMNI&tab=OCULDiscoveryNetwork&mode=basic&displayMode=full&bulkSize=10&highlight=true&dum=true&query=any,contains,' + $(".map-box").val() + '&displayField=all&search_scope=OCULDiscoveryNetwork&pcAvailabiltyMode=true';
	a.target= '_blank';
	a.href= response;
	a.click();
      return false; 
   }
});
  </script>
  
  