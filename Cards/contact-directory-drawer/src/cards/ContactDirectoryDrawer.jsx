import React from 'react';
import {
    makeSty<PERSON>,
    Drawer,
    Button
} from '@ellucian/react-design-system/core';
import "./styles.css";

const useStyles = makeStyles({
    fullList: {
        width: 'auto',
    },
    margin: {
        margin: 0,
    }
});

export default function ContactDirectoryDrawer() {
    const classes = useStyles();
    const [state, setState] = React.useState({
        top: false,
        left: false,
        bottom: false,
        right: false,
    });

    const toggleDrawer = (anchor, open) => (event) => {
        if (event.type === 'keydown' && (event.key === 'Tab' || event.key === 'Shift')) {
            return;
        }

        setState({ ...state, [anchor]: open });
    };

   

    return (
        <div style={{ height : "100%" }} >
            {['left'].map((anchor) => (
                <React.Fragment key={anchor}>
                    <iframe title="test" src="https://erpapps.ocadu.ca/myEmployeeDirectory/" style={{ border: 0, width: '100%', height : "100%" }} />
                    <Button onClick={toggleDrawer(anchor, true)} aria-expanded={state[anchor]} aria-haspopup="true" aria-controls={anchor} id="contactdrawer-button" className={classes.margin}>Expand</Button>
                    <Drawer className="contact-drawer" id={anchor} PaperProps={{ component: 'nav' }} anchor={anchor}
                        open={state[anchor]}
                        onClose={toggleDrawer(anchor, false)}
                    >
                        <iframe title="test" src="https://erpapps.ocadu.ca/myEmployeeDirectory/" style={{ border: 0, width: '100%' }} />
                        <Button onClick={toggleDrawer(anchor, false)} aria-expanded={state[anchor]} aria-haspopup="true" aria-controls={anchor} id="contactdrawer-button-close" className={classes.margin}>Close</Button>
                    
                    </Drawer>
                </React.Fragment>
            ))}
        </div>
    );
}
