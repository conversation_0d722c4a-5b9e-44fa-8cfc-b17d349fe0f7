#contactdrawer-button {
    position: absolute;
    bottom: 0px;
    right: 0px;
    background-color: #2f6af0;
    border: 1px solid #2f6af0;
    border-radius: 6px 0px 6px 0px;
}

#contactdrawer-button:hover {
    color: #2f6af0;
    background-color: #fff;
    border-color: #2f6af0;
}

.contact-drawer nav {
 padding: 20px 10px 10px 10px;
 display: flex;
}

.contact-drawer iframe {
height: 100%;
height: 100%;
display: flex;
align-items: stretch;
}

#contactdrawer-button-close {
    position: absolute;
    bottom: 0px;
    right: 0px;
    background-color: #2f6af0;
    border: 1px solid #2f6af0;
    border-radius: 6px 0px 0px 0px;
}

#contactdrawer-button-close:hover {
    color: #2f6af0;
    background-color: #fff;
    border-color: #2f6af0;
}

.contact-drawer {
width: 40vw;
max-width: 800px;
}

div[data-extension-name="ContactDirectoryDrawer"] .MuiCardContent-root div div {
overflow-y: hidden;
}

div[data-extension-name="ContactDirectoryDrawer"] .MuiCardContent-root div div {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
}
div[data-extension-name="ContactDirectoryDrawer"] .MuiCardContent-root div div::-webkit-scrollbar { 
    display: none;  /* Safari and Chrome */
}

@media(max-width: 880px) {
    .contact-drawer {
    width: 100vw !important
    }
}

@media (min-width: 480px) {
    .contact-drawer {
        width: 100vw !important
    }
}
@media (min-width: 880px) {
    .contact-drawer {
        width: 40vw !important
    }
}