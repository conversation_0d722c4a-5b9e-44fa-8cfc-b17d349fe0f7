{"messages": {"CacheCard-reset": "Reset", "CacheCard-viewCount": "View Count: {viewedCount}", "DrilldownCard-clickMe": "Click me", "DrilldownCard-clicks": "Clicks: {count}", "DrilldownCard-goBack": "Go back", "DrilldownCard-message": "You clicked {count} times", "ErrorMessageCard-buttonAria": "Set error message", "ErrorMessageCard-description": "Set an error message to display", "ErrorMessageCard-headerMessage": "Header message", "ErrorMessageCard-iconColor": "Icon color", "ErrorMessageCard-iconName": "Icon name", "ErrorMessageCard-textMessage": "Text message", "ErrorMessageCard-submit": "Submit", "GraphQLQueryCard-noBuildings": "No buildings to show", "GraphQLQueryCard-noSelectedSite": "Select a site to show related buildings", "GraphQLQueryCard-sites": "Sites", "GraphQLQueryCard-fetchFailed": "<PERSON><PERSON> failed", "GraphQLQueryCard-sitesFetchFailed": "Could not fetch sites", "GraphQLQueryCard-buildingsFetchFailed": "Could not fetch buildings", "LoadingStateCard-label": "Set the loading status for 10 seconds", "LoadingStateCard-reload": "Reload", "LoadingStateCard-status": "Status: {status}", "MarkdownTemplate-markdown-placeholder": "Enter Markdown text", "MarkdownTemplate-color-placeholder": "Choose your color", "MarkdownTemplate-valid-colors": "Valid colors: {colors}", "Manifest-description": "Props Description", "Manifest-displayCardType": "Props Display Title", "Manifest-title": "Props Title", "Page-title": "Props and Hooks Title", "PreventRemoveCard-switchLabel": "Toggle ability to remove card:", "ThrowErrorCard-errorHeaderMessage": "An expected error occurred", "ThrowErrorCard-errorTextMessage": "This error was caught by the extension code and setErrorMessage was intentionally called", "ThrowErrorCard-extensionErrorButtonText": "Throw Extension Error", "ThrowErrorCard-dashboardErrorButtonText": "Throw Dashboard Error"}}