#button-list button {
padding: 20px 20px;
margin-bottom: 0;
border-radius: 0px;
}

#button-list button svg {
flex: none !important;
margin-right: 15px;
height: 1.5rem;
}

#button-list button {
background-color: #1DB0F6;
justify-content: left;
font-family: "Figtree", Arial, sans-serif;
text-transform: none;
letter-spacing: 0px;
z-index: 9;
transition: 0.25s all;

}

#button-list button:hover {
width: calc(100% + 1rem);
z-index: 99;
}


#button-list button:nth-child(2n){
background-color: #3CAB49 !important;
}

#button-list button:nth-child(3n){
background-color: #A014EC !important;
}

#button-list button:nth-child(4n){
background-color: #F7FE4F !important;
color: #000;
}

#button-list button:nth-child(5n){
background-color: #F81423 !important;
}

#button-list button:nth-child(6n){
background-color: #086CF9 !important;
}

#button-list button:nth-child(7n){
background-color: #F26323 !important;
}


/* config styles */
table#button-list-config {
table-layout: fixed;
}

table#button-list-config td, table#button-list-config th {
max-width: 33%;
}
    
table#button-list-config td div, table#button-list-config th div  {
width: 100%;
}

button.add-button {
margin-top: 20px;
}