import React from "react";
import PropTypes from "prop-types";
import {
  <PERSON>Field,
  IconButton,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography
} from "@ellucian/react-design-system/core";
import { Icon } from "@ellucian/ds-icons/lib";
const QuickButtonLinksTemplateConfig = (props) => {
  const {
    cardControl: { setCustomConfiguration, setIsCustomConfigurationValid },
    cardInfo: {
      configuration: { customConfiguration }
    }
  } = props;

  const client = customConfiguration ? customConfiguration.client : undefined;
  const [links, setLinks] = React.useState(
    client ? client.links : [{ label: "", url: "", icon: "" }]
  );

  const handleChange = (i, e) => {
    const newLinks = [...links];
    newLinks[i][e.target.name] = e.target.value;
    setLinks(newLinks);

    setCustomConfiguration({
      customConfiguration: {
        client: {
          links: newLinks
        }
      }
    });
  };

  const addLink = () => {
    setLinks([...links, { name: "", email: "", icon: "" }]);
  };

  const removeLink = (i) => {
    const newLinks = [...links];
    newLinks.splice(i, 1);
    setLinks(newLinks);
  };

  const handleBlur = (e) => {
    setIsCustomConfigurationValid(e.target.value !== "");
  };

  return (
    <div>
      <Typography variant="h3">To find the names of icons available, please visit the <a href="https://fontawesome.com/search?ip=classic&s=solid&o=r"> Font Awesome Library</a></Typography>
      <Table id="button-list-config">
        <TableHead>
          <TableRow>
            <TableCell>Text</TableCell>
            <TableCell>Url</TableCell>
            <TableCell>Icon</TableCell>
            <TableCell>Action</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {links.map((element, index) => {
            return (
              <TableRow key={index}>
                <TableCell component="th" scope="row">
                  <TextField
                    label="Label"
                    margin="normal"
                    name="label"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.label}
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    label="URL"
                    margin="normal"
                    name="url"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.url}
                  />
                </TableCell>
                <TableCell>
                  <TextField
                    label="Icon"
                    margin="normal"
                    name="icon"
                    onBlur={handleBlur}
                    onChange={(e) => handleChange(index, e)}
                    value={element.icon}
                  />
                </TableCell>
                <TableCell>
                  <IconButton
                    color="gray"
                    disabled={!index}
                    onClick={() => removeLink(index)}
                  >
                    <Icon color="red" name="trash" />
                  </IconButton>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
      <IconButton className="add-button" onClick={() => addLink()}>
        <Icon name="add" />
      </IconButton>
    </div>
  );
};

QuickButtonLinksTemplateConfig.propTypes = {
  cardControl: PropTypes.object,
  cardInfo: PropTypes.object
};

export default QuickButtonLinksTemplateConfig;
