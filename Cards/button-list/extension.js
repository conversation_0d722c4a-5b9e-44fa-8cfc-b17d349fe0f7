module.exports = {
    name: '<PERSON>ton List',
    publisher: '<PERSON>',
    group: 'OCAD University',
    cards: [{
        type: "Button List",
        source: "./src/cards/QuickButtonLinksCard",
        title: "Button List",
        displayCardType: "Button List",
        description: "List of links as buttons",
        template: {
            icon: "align-justify",
            title: "Button List",
            description: "Quick Link lists where links are stylized like buttons"
        },
        customConfiguration: {
            source: "./src/cards/QuickButtonLinksTemplateConfig.jsx"
        }
    }],
    page: {
        source: './src/page/router.jsx'
    }
};