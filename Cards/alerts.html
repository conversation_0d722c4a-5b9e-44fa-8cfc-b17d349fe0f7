<style>


* {
	font-family: Gotham, Helvetica Neue, Helvetica, -apple-system, BlinkMacSystemFont, Open Sans, Source Sans Pro, "Droid Serif", Aria<PERSON>, Segoe UI, sans-serif;
	transition: all 0.15s ease-out;
	box-sizing: border-box;
}
body, html {
padding: 0px;
margin: 0px;
height: 100%;
overflow-x: hidden;
}

body {
padding-bottom: 60px;
}


.alert-card {
font-family: "Roboto", sans-serif;
margin: 10px 0px 0px 4vw;
width: calc(100vw - 12vw) !important;
padding: 5px;
border-radius: 6px;
background: #fff;
border: 1px solid #e3e3e3;

display: grid;
grid-template-columns: 1fr 20fr;
grid-template-rows: 1fr;
grid-column-gap: 20px;
grid-row-gap: 0px;
}

.alert-card:hover {
background: #000;
color: #fff;
}

.slider a {
color: #000 !important;
text-decoration: none;
}

.slider a:hover {
color: #888 !important;
}

.alert-card h3 {
font-size: 5vw;
margin: 0px 0px 15px 0px;
display: block;
font-weight: 600 !important;
}

.alert-card .status.False {
background-color: orange;
border-radius: 6px;
}

.alert-card .status.True {
background-color: red;
border-radius: 6px;
}

.alert-card .date {
font-size: 4vw !important;
margin-bottom: 5px;
font-weight: 200;
display: block;
}

.alert-card .title {
font-weight: 200;
}

.card-container {
position: relative;
}

.controls {
position: fixed;
z-index: 99;
bottom: 10px;
right: 11px;
}

.controls button { 
background: #EFEFEF;
color: #000;
padding: 10px 25px 10px 25px;
border: 0px;
}

.controls button:first-of-type {
border-right: 1px solid #000;
border-radius: 0 0 0px 12px;
}


.controls button:nth-of-type(2) {
border-radius: 0 0 12px 0px;
}

.controls button:hover { 
color: #ffff;
background: #000;
}

.info-panel {
width: 100%;
}

.cta-banner {
position: fixed;
bottom: 0;
left: 0;
z-index: 99;
background: #000;
color: #fff;
height: 43px;
width: 100%;
font-family: "Roboto", sans-serif;
font-size: 15px;
padding: 12px 40px 6px 10px;
}

.cta-banner:hover {
color: #000;
background-color: #fff;
}
</style>


<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,700;1,400&display=swap" rel="stylesheet">


<div class="card-container">
  <div class="slider">

  </div>
  <div class="controls"></div>
  
  <a href="https://live-ocad-dpl.pantheonsite.io/accessibility-service-disruptions" target="top">
    <div class="cta-banner">See all Service Alerts ⟶</div>
  </a>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-migrate/3.5.2/jquery-migrate.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.9.0/slick.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.30.1/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.7.8/handlebars.min.js" ></script>




<script id="each-template" type="text/x-handlebars-template">
{{#each this}}
    <div class="alert-card">
      
      <div class="status {{this.status}}">&nbsp;</div>
      <div class="info-panel">
        <h3 class="title">{{this.field_title}}</h3>
        
        <span class="date">{{{this.field_alert_publish_date}}}</span>
        <p>{{{this.message__value}}}</p>
      </div>
    
    </div>
{{/each}}
</script>


<script>
  
var template;
 

$.ajax({
url: 'https://live-ocad-dpl.pantheonsite.io/api/alerts',
type: "GET",
dataType: "json",
success: function (data) {   
  template = Handlebars.compile($("#each-template").html());
  $('.slider').html(template(data));
  
   
  $(".date").each(function( index ) {
	var date = new Date($(this).children().first().attr("datetime"));
	$(this).html(moment(date).format("MMMM Do YYYY h:mma"));
	});
}
});
</script>

