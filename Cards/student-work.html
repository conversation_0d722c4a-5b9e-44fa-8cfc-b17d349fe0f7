<style>

body, html {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
}
body::-webkit-scrollbar, html::-webkit-scrollbar { 
    display: none;  /* Safari and Chrome */
}




* {
	font-family: Gotham, Helvetica Neue, Helvetica, -apple-system, BlinkMacSystemFont, Open Sans, Source Sans Pro, "Droid Serif", Arial, Segoe UI, sans-serif;
	transition: all 0.15s ease-out;
}

  /* Slider */
.slick-slider
{
    position: relative;

    display: block;
    box-sizing: border-box;

    -webkit-user-select: none;
       -moz-user-select: none;
        -ms-user-select: none;
            user-select: none;

    -webkit-touch-callout: none;
    -khtml-user-select: none;
    -ms-touch-action: pan-y;
        touch-action: pan-y;
    -webkit-tap-highlight-color: transparent;
}

.slick-list
{
    position: relative;

    display: block;
    overflow: hidden;

    margin: 0;
    padding: 0;
}	
.slick-list:focus
{
    outline: none;
}
.slick-list.dragging
{
    cursor: pointer;
    cursor: hand;
}

.slick-slider .slick-track,
.slick-slider .slick-list
{
    -webkit-transform: translate3d(0, 0, 0);
       -moz-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
         -o-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
}

.slick-track
{
    position: relative;
    top: 0;
    left: 0;

    display: block;
    margin-left: auto;
    margin-right: auto;
}
.slick-track:before,
.slick-track:after
{
    display: table;

    content: '';
}
.slick-track:after
{
    clear: both;
}
.slick-loading .slick-track
{
    visibility: hidden;
}

.slick-slide
{
    display: none;
    float: left;

    height: 100%;
    min-height: 1px;
}
[dir='rtl'] .slick-slide
{
    float: right;
}
.slick-slide img
{
    display: block;
}
.slick-slide.slick-loading img
{
    display: none;
}
.slick-slide.dragging img
{
    pointer-events: none;
}
.slick-initialized .slick-slide
{
    display: block;
}
.slick-loading .slick-slide
{
    visibility: hidden;
}
.slick-vertical .slick-slide
{
    display: block;

    height: auto;

    border: 1px solid transparent;
}
.slick-arrow.slick-hidden {
    display: none;
}



body, html {
padding: 0px;
margin: 0px;
height: 100%;
overflow-x: hidden;
}

.slick-slider {
z-index: 9;
}

.slick-slide > div {
width: 100% !important;
height: auto;
}

.slider-card {
font-family: "Roboto", sans-serif;
margin: 0px;
width: 100% !important;
height: 100vh;
padding: 0px;
}

.slider-card img {
 display: none;
}

.card-container {
position: relative;
}

.controls {
position: fixed;
z-index: 99;
top: 0px;
right: 11px;
}

.controls button { 
background: #EFEFEF;
color: #000;
padding: 10px 25px 10px 25px;
border: 0px;
}

.controls button:first-of-type {
border-right: 1px solid #000;
border-radius: 0px 0px 0px 12px;
}


.controls button:nth-of-type(2) {
border-radius: 0px 0px 12px 0px;
}

.controls button:hover { 
color: #ffff;
background: #000;
}


.card-image {
width: 100%;
height: 100%;
background-color: #000;
float: left;
background-size: cover !important;
background-repeat: no-repeat !important;
background-position: center center !important;
}


.credit-banner {
display: inline-block;
background: rgba(0,0,0,0.8);
color: #fff;
height: 25px;
font-family: "Roboto", sans-serif;
font-size: 14px;
padding: 12px 40px 6px 10px;
border-radius: 0px 12px 0px 0px;
position: absolute;
bottom: 0px;
text-decoration: none;
}



</style>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,700;1,400&display=swap" rel="stylesheet">


<div class="card-container event-slider">
  <div class="slider">

  </div>
  <div class="controls"></div>



</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-migrate/3.5.2/jquery-migrate.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.9.0/slick.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.30.1/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.7.8/handlebars.min.js" ></script>




<script id="each-template" type="text/x-handlebars-template">
{{#each this}}
    <div class="slider-card">
      <div class="card-image" style="background: url('https://ocadu.ca{{this.field_media_image}}')">
      	<a  class="credit-banner" href="{{this.field_link}}" target="_blank"><div>{{this.field_author}}, {{{this.field_program_name}}}, {{this.field_graduation_year}}</div></a>
      </div>
    	  
 	</div>
{{/each}}
</script>


<script>
  
var template;

$.ajax({
url: 'https://www.ocadu.ca/api/studentwork',
type: "GET",
dataType: "json",
success: function (data) {   
  template = Handlebars.compile($("#each-template").html());
  $('.slider').html(template(data));
  
  $('.slider').slick({
    appendArrows: $('.controls'),
    nextArrow: '<button type="button" class="slick-next">►</button>',
    prevArrow: '<button type="button" class="slick-prev">◄</button>',
    autoplay: true,
    speed: 1000,
    autoplaySpeed: 5000
  });
  
}
});
</script>

