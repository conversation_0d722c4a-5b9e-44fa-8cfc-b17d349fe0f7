import React, { useEffect, useState } from 'react';
import { Typography, makeStyles } from '@ellucian/react-design-system/core';
import "./styles.css";

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(5), // 40px padding (5 * 8px)
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    borderTop: '10px solid #cccccc' // 10px grey border at the top
  },
  status: {
    marginTop: 'auto'
  }
}));

export default function AlertInjector() {
  const classes = useStyles();
  const [status, setStatus] = useState('Initializing...');
  const [alertCount, setAlertCount] = useState(0);

  useEffect(() => {
    // Function to inject alerts into the carousel
    const injectAlerts = async () => {
      try {
        
          // We're not in an iframe, can directly access the document
          setStatus('Checking for carousel container...');
          
          // Check if the carousel container exists
          const carouselContainer = document.querySelector("#sliding-carousel-container");
          if (!carouselContainer) {
            setStatus('Carousel container not found on this page');
            return;
          }
          
          // Find the inner div to append to
          const innerDiv = carouselContainer.querySelector("div");
          if (!innerDiv) {
            setStatus('Inner div not found in carousel container');
            return;
          }
          
          setStatus('Fetching alerts from API...');
          
          // Fetch alerts from the API
          const response = await fetch('https://www.ocadu.ca/api/alerts');
          const alerts = await response.json();
          
          // Debug: Log the first alert to see its structure
          console.log('First alert structure:', alerts.length > 0 ? alerts[0] : 'No alerts');
          
          // Filter alerts with status: true or "True"
          const activeAlerts = alerts.filter(alert => 
            alert.status === true || 
            alert.status === "True" || 
            String(alert.status).toLowerCase() === "true"
          );
          
          if (activeAlerts.length === 0) {
            setStatus('No active alerts found');
            return;
          }
          
          setStatus(`Injecting ${activeAlerts.length} alert(s) into carousel...`);
          
          // Create and append alert elements
          activeAlerts.forEach((alert, index) => {
            // Check if this alert is already injected (avoid duplicates)
            const existingAlert = document.querySelector(`[data-alert-id="${alert.nid || alert.id || 'unknown'}"]`);
            if (existingAlert) {
              return;
            }
            
            // Create alert element with the same structure as native alerts
            const alertElement = document.createElement('div');
            alertElement.className = 'MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation5 MuiCard-root jssQrKj-9qIK51642 jssQrKj-9qIK51467 jssQrKj-9qIK51646 jssQrKj-9qIK51468 jssQrKj-9qIK51647 jssQrKj-9qIK51469 jssQrKj-9qIK51648 jssQrKj-9qIK51643 jssQrKj-9qIK51644 jssQrKj-9qIK51635 jssQrKj-9qIK51633 eds-ltr-pq0mgr injected-alert';
            alertElement.setAttribute('data-alert-id', alert.nid || alert.id || 'unknown');
            alertElement.style.transform = 'translateX(-6px)';
            
            // Get content from various possible fields
            const title = alert.title || alert.field_title || '';
            const body = alert.body || alert.message__value || alert.message || '';
            
            // Combine title and body for the message
            const message = title ? (body ? `<strong>${title}</strong><br>${body}` : title) : body;
            
            // Create the inner structure (without the close button)
            alertElement.innerHTML = `
              <div class="MuiCardContent-root jssQrKj-9qIK51649 hedtech-reset-spacing jssQrKj-9qIK51636 eds-ltr-1qw96cp">
                <div class="MuiTypography-root jssQrKj-9qIK51427 MuiTypography-body1 jssQrKj-9qIK51437 jssQrKj-9qIK51637 eds-ltr-te2hye" data-testid="announcements_message">
                  ${message}
                </div>
                <div class="jssQrKj-9qIK51638">
                  <span class="MuiTypography-root jssQrKj-9qIK51427 MuiTypography-body2 jssQrKj-9qIK51438 jssQrKj-9qIK51641 eds-ltr-racexk" data-testid="announcements_sequence">
                    ${index + 1} of ${activeAlerts.length}
                  </span>
                </div>
              </div>
            `;
            
            // Append to carousel
            innerDiv.appendChild(alertElement);
          });
          
          setAlertCount(activeAlerts.length);
          setStatus(`Successfully injected ${activeAlerts.length} alert(s) into carousel`);
        
      } catch (error) {
        console.error('Error:', error);
        setStatus(`Error: ${error.message}`);
      }
    };

    // Run the injection function
    injectAlerts();
    
    // Set up a periodic check (every 5 minutes)
    const intervalId = setInterval(injectAlerts, 5 * 60 * 1000);
    
    // Clean up on unmount
    return () => {
      clearInterval(intervalId);
    };
  }, []);

  return (
    <div className={classes.root}>
      <Typography variant="h3">Alert Injector</Typography>
      <Typography variant="body1">
        This card checks for the sliding carousel container and injects active alerts from the OCADU API.
      </Typography>
      
      <div className={classes.status}>
        <Typography variant="h4">Status:</Typography>
        <Typography variant="body2">{status}</Typography>
        
        {alertCount > 0 && (
          <Typography variant="body2" style={{ marginTop: '8px', color: 'green' }}>
            {alertCount} alert(s) injected
          </Typography>
        )}
      </div>
    </div>
  );
}
