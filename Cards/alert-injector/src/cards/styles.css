/* Additional styles for injected alerts */
.injected-alert {
  margin: 10px 0;
  width: 25%;
  height: 11rem;
  padding: 40px;
  text-align: center;
  border-top: 16px solid #888;
}

/* Status indicator styles */
.status-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-success {
  background-color: #28a745;
}

.status-warning {
  background-color: #ffc107;
}

.status-error {
  background-color: #dc3545;
}

.status-info {
  background-color: #17a2b8;
}

/* Styles for the injected alerts in the carousel */
.injected-alert .MuiCardContent-root {
  padding: 16px;
}


