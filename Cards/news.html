<style>

 

* {
	font-family: Gotham, Helvetica Neue, Helvetica, -apple-system, BlinkMacSystemFont, Open Sans, Source Sans Pro, "Droid Serif", Aria<PERSON>, Segoe UI, sans-serif;
	transition: all 0.15s ease-out;
	box-sizing: border-box;
}

body, html {
padding: 0px 0px 15vh 0px;
margin: 0px;
height: 100%;
overflow-x: hidden;
}


.news-card {
font-family: "Roboto", sans-serif;
width: 100%;
margin: 10px 0px 15px 20px;
width: calc(100% - 40px) !important;
padding: 0px;
border-radius: 5px;
background: #fff;
border: 1px solid #d3d3d3;
display: flex;
}

.news-card:hover {
background: #000;
color: #fff;
}

.slider a {
color: #000 !important;
text-decoration: none;
}


.slider a:hover {
color: #888 !important;
}
.news-card h3 {
font-size: 4.75vw;
margin: 0px 0px 15px 0px;
display: block;
}

.news-card .date-raw {
font-size: 4vw;
margin-bottom: 0px;
display: block;
}

.news-card img {
 display: none;
}

.card-container {
position: relative;
}

.controls {
position: fixed;
z-index: 99;
bottom: 10px;
right: 11px;
}

.controls button { 
background: #EFEFEF;
color: #000;
padding: 10px 25px 10px 25px;
border: 0px;
}

.controls button:first-of-type {
border-right: 1px solid #000;
border-radius: 0 0 0px 12px;
}


.controls button:nth-of-type(2) {
border-radius: 0 0 12px 0px;
}

.controls button:hover { 
color: #ffff;
background: #000;
}


.news-image {
border-radius: 4px;
width: 35%;
background-size: cover;
background-position: center center;
margin-right: 3%;
}

.info-panel {
padding: 5px;
width: 65%;
}

.cta-banner {
position: fixed;
bottom: 0;
left: 0;
z-index: 99;
background: #000;
color: #fff;
height: 43px;
width: 100%;
font-family: "Roboto", sans-serif;
font-size: 15px;
padding: 12px 40px 6px 10px;
}

.cta-banner:hover {
color: #000;
background-color: #fff;
}

</style>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,700;1,400&display=swap" rel="stylesheet">


<div class="card-container">
  <div class="slider">

  </div>
  <div class="controls"></div>
  
  <a href="https://ocadu.ca/news" target="_blank">
    <div class="cta-banner">See all News ⟶</div>
  </a>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-migrate/3.5.2/jquery-migrate.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.9.0/slick.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.30.1/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.7.8/handlebars.min.js" ></script>




<script id="each-template" type="text/x-handlebars-template">
{{#each this}}
  <a href="{{this.view_node}}" target="_blank">
    <div class="news-card">
      
      <div class="news-image" style="background-image: url('https://live-ocad-dpl.pantheonsite.io/{{#if this.field_media_image}}{{{this.field_media_image}}}{{else}}sites/default/files/styles/1_5_1_small/public/2024-06/header-image_1.jpg.webp?itok=h_87Lq7i{{/if}}');">&nbsp;</div>
      <div class="info-panel">
        <h3 class="title">{{{this.title}}}</h3>
        <span class="date-raw">{{{this.field_news_date}}}</span>
      </div>
    
    </div>
  </a>
{{/each}}
</script>


<script>
  
var template;
 

$.ajax({
url: 'https://www.ocadu.ca/api/news',
type: "GET",
dataType: "json",
success: function (data) {   
  template = Handlebars.compile($("#each-template").html());
  $('.slider').html(template(data));
  
  $(".date-raw").each(function( index ) {
	var date = new Date($(this).children().first().attr("datetime"));
	$(this).html(moment(date).format("MMMM Do YYYY"));
});

  
}
});

   
</script>

