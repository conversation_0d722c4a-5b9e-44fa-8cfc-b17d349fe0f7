
#button-grid {
display: flex;
flex-wrap: wrap;
gap: 8px 8px;
padding-bottom: 20px;
}

#button-grid button:last-child{
flex-grow: 0;
width: calc(50%);
flex: none;
}

#button-grid button {
flex: 1 1 calc(50% - 20px);
margin-bottom: 0px;
background: #000;
text-align: left;
align-items: start;
padding: 15px;
line-height: 19px;
display: grid;
grid-template-columns: 25px auto;
font-size: 14px;
align-items: start;
justify-content: left;
gap: 10px;
border-radius: 6px;
font-family: Gotham, Helvetica Neue, Helvetica, -apple-system, BlinkMacSystemFont, Open Sans, Source Sans Pro, "Droid Serif", Arial, Segoe UI, sans-serif;
letter-spacing: 0px;
font-weight: 700;
text-transform: none;
}

#button-grid button.wide-true {
flex: 1 0 calc(100% - 20px);
}

#button-grid button svg {
flex: none;
}

#button-grid button:nth-child(1n):hover {
background: #5da955;
}

#button-grid button:nth-child(2n):hover {
background: #53adf0;
color: #000;
}

#button-grid button:nth-child(2n):hover img {
filter: invert(1);
}

#button-grid button:nth-child(3n):hover {
background: #9325e3;
}

#button-grid button:nth-child(4n):hover {
background: #2f6af0;
}

#button-grid button:nth-child(5n):hover {
background: #e43732;
}

#button-grid button:nth-child(6n):hover {
background: #e16c38;
}

#button-grid button:nth-child(7n):hover {
background: #f3e65b;
color: #000;
}

#button-grid button:nth-child(7n):hover img {
filter: invert(1);
}

#button-grid .svg-inline--fa {
height: 1.5em;
}



/* config styles */
table#button-grid-config {
table-layout: fixed;
}

table#button-grid-config td, table#button-grid-config th {
max-width: 33%;
}
    
table#button-grid-config td div, table#button-grid-config th div  {
width: 100%;
}

button.add-button {
margin-top: 20px;
}