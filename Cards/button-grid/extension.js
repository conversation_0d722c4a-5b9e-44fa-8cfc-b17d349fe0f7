module.exports = {
    name: '<PERSON><PERSON> Grid',
    publisher: '<PERSON>',
    group: 'OCAD University',
    cards: [{
        type: "Button Grid",
        source: "./src/cards/QuickButtonGridCard",
        title: "Button List",
        displayCardType: "Button List",
        description: "List buttons",
        template: {
            icon: "line-columns",
            title: "Button Grid",
            description: "Grid of optional buttons to display tools or options"
        },
        customConfiguration: {
            source: "./src/cards/QuickButtonGridCardConfig.jsx"
        }
    }],
    page: {
        source: './src/page/router.jsx'
    }
};