<html><head><meta charset="utf-8"><title>Navigate myOCADU</title></head><body><style>

body {
background: #f79623;
}

#button-grid {
margin-top: -30px;
}

#button-grid a {
z-index: 99;
width: calc(50% - 39px);
float: left;
margin-bottom: 0px;
background: #000;
text-align: center;
padding: 15px;
line-height: 19px;
font-size: 13px;
border-radius: 6px;
font-family: Gotham, Helvetica Neue, Helvetica, -apple-system, BlinkMacSystemFont, Open Sans, Source Sans Pro, "Droid Serif", Arial, Segoe UI, sans-serif;
letter-spacing: 0px;
font-weight: 700;
text-transform: none;
color: #fff;
text-decoration: none;
display: block;
border: 2px solid #000;
position: relative;
}

#button-grid a:hover {
background-color: #fff !important;
color: #000;
}

img {
z-index: 9;
position: relative;
}

img:hover {
transition: all 0.1s linear;
transform: scale(1.1, 1.1);
}

#button-grid a:nth-of-type(2)  {
margin-right: 5px;
}


#button-grid a:nth-of-type(3)  {
margin-left: 5px;
}

#button-grid a.wide-true {
margin-bottom: 10px;
width: calc(100% - 35px);
}

#button-grid {
padding-bottom: 20px;
}






#button-grid a svg {
flex: none;
}

#button-grid a:nth-child(1n):hover {
background: #5da955;
}

#button-grid a:nth-child(2n):hover {
background: #53adf0;
color: #000;
}

#button-grid a:nth-child(2n):hover img {
filter: invert(1);
}

#button-grid a:nth-child(3n):hover {
background: #9325e3;
}

#button-grid a:nth-child(4n):hover {
background: #2f6af0;
}

#button-grid a:nth-child(5n):hover {
background: #e43732;
}

#button-grid a:nth-child(6n):hover {
background: #e16c38;
}

#button-grid a:nth-child(7n):hover {
background: #f3e65b;
color: #000;
}

#button-grid a:nth-child(7n):hover img {
filter: invert(1);
}

#button-grid .svg-inline--fa {
height: 1.5em;
}



/* config styles */
table#button-grid-config {
table-layout: fixed;
}

table#button-grid-config td, table#button-grid-config th {
max-width: 33%;
}
    
table#button-grid-config td div, table#button-grid-config th div  {
width: 100%;
}

button.add-button {
margin-top: 20px;
}
</style>

<body>

<a target="_blank" href="https://experience.elluciancloud.ca/ou/?category=career">
<img src="https://www.ocadu.ca/sites/default/files/myOCADU/images/pmt.jpg" style="width: 100%; margin-bottom: 30px;">
</a>

<div id="button-grid">
	<a style="background-color: #086CF9; border-color: #086CF9;"  target="_blank" href="https://experience.elluciancloud.ca/ou/?category=career" class="MuiButtonBase-root MuiButton-root jssCOOSdhKPaX42 jssCOOSdhKPaX43 MuiButton-contained jssCOOSdhKPaX46 MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-fullWidth MuiButton-root jssCOOSdhKPaX42 jssCOOSdhKPaX43 MuiButton-contained jssCOOSdhKPaX46 MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-fullWidth jssCOOSdhKPaX41 wide-true eds-ltr-1wsy4qd" tabindex="0" type="button">
		
		Pay tuition and fees
	</a>
	<a  style="background-color: #A014EC; border-color: #A014EC;" target="_blank" href="https://www.ocadu.ca/student-services/student-accounts/how-pay-tuition" tabindex="0" type="button">
		
		More information
	</a>
	<a  style="background-color: #1AB0F6; border-color: #1AB0F6;" target="_blank" href="https://ocadu.topdesk.net/tas/public/ssp/content/serviceflow?unid=975bf2906b4b45359aed8e830c420edc&openedFromService=true" tabindex="0" type="button">
		
		Get support
	</a>
	
</div>

