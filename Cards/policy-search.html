<style>
	* {
	font-family: "Gotham", sans-serif;
	transition: all 0.15s ease-out;
	}
	body {
	background: #000;
	}
	.search-header {
	z-index: 99;
	position: sticky;
	top: 0px;
	left: 0px;
	padding: 2vw 2vw 12px 0vw;
	width: 100%;
	background: #000;
	}
	#policy-search {
	box-sizing: border-box;
	font-size: clamp(20px,6vw,24px);
	margin-bottom: 0px;
	background: #fff;
	border-radius: 6px;
	border: 1px solid #333;
	padding: 2vw;
	width: 100%;
	}
	li {
	min-height: 52px;
	box-sizing: border-box;
	width: 100%;
	background: #fff;
	color: #000;
	margin-bottom: 6px;
	display: block;
	padding: 2vw !important;
	border-radius: 6px;
	font-size: clamp(12px,5vw,14px);
	border: 1px solid #333;
	display: flex;
	column-gap: 20px;
	align-items: start; 
	flex-wrap: wrap;
	position: relative;
	align-items: stretch;
	}
	a {
	text-decoration: none;
	}
	ul {
	margin-top: 0px;
	padding-left: 0px;
	}
	li:hover {
	background: #056CF9;
	color: #fff;
	}
	li:hover .download {
	background: #000;
	color: #fff !important;
	}
	li span {
	flex-basis: 10%;
	font-weight: 800;
	font-size: clamp(12px,5vw,14px);
	}
	li h6 {
	margin: 0px;
	flex-basis: 55%;
	font-weight: 400;
	font-size: clamp(12px,5vw,14px);
	}
	li p {
	margin: 10px 0px 0px 0px;
	flex-basis: 55%;
	font-size: clamp(12px,5vw,14px);
	margin-left: calc(10% + 20px);
	}
	li .download {
	flex-basis: 15%;
	font-size: clamp(10px,4vw,12px);
	color: #000;
	text-decoration: none;
	border: 1px solid #000;
	padding: 10px;
	text-align: center;
	position: absolute;
	top: 2vw;
	right: 2vw;
	max-width: 18vw;
	}
	li .download:hover {
	background: #000;
	color: #fff !important;
	}
</style>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.7.8/handlebars.min.js" ></script>


<div class="search-header">
	<input id="policy-search" type="text" placeholder="Search policies...">
</div>
<ul id="policies">
</ul>

<script id="each-template" type="text/x-handlebars-template">
{{#each this}}
  <a href="{{#if this.field_link}}{{{this.field_link}}}{{else}}{{this.media_download_link}}{{/if}}" target="_blank" class="policy">
		<li>
			<span>{{this.field_admin_policy_number}}</span>
			<h6>{{this.title}}</h6>
			<div class="download">Download</div>
		</li>
	</a>
{{/each}}
</script>

<script>
var template;

$.ajax({
	url: 'https://www.ocadu.ca/api/policies',
	type: "GET",
	dataType: "json",
	success: function (data) {   
	  template = Handlebars.compile($("#each-template").html());
	  $('#policies').html(template(data));
	}
});


	$(document).ready(function() {
	  $("#policy-search").on("keyup", function() {
	    var value = $(this).val().toLowerCase();
	
	    $("#policies > a").show().filter(function() {                           //Select all li and show
	      return $(this).text().toLowerCase().indexOf(value) === -1;  //Find the h6 and check if the value is substring of the text. Return true if not found.
	    }).hide();                                                          //Hide all li that that the value is not the substring of text
	  });
	});
	
</script>