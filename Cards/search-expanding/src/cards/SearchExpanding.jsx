import React from 'react';
import "./styles.css";
 
import {
    makeSty<PERSON>,
    Drawer,
    But<PERSON>
} from '@ellucian/react-design-system/core';


const useStyles = makeStyles({
    fullList: {
        width: 'auto',
    },
    margin: {
        margin: 0,
    }
});



export default function SearchExpanding() {
    const classes = useStyles();
    const [searchValue, setSearchValue] = React.useState('');
    const [iframeUrl, setIframeUrl] = React.useState('https://searchable.ocadu.ca/?embdded=true&search=');
    const [state, setState] = React.useState({
        top: false,
        left: false,
        bottom: false,
        right: false,
    });

    // Handle search input change
    const handleSearchChange = (event) => {
        setSearchValue(event.target.value);
    };

    // Handle search submission
    const handleSearchSubmit = (event, anchor) => {
        event.preventDefault();
        // Update the iframe URL with the search term
        setIframeUrl(`https://searchable.ocadu.ca/?embdded=true&search=${encodeURIComponent(searchValue)}`);
        // Open the drawer
        toggleDrawer(anchor, true)(event);
    };

    const toggleDrawer = (anchor, open) => (event) => {
        if (event.type === 'keydown' && (event.key === 'Tab' || event.key === 'Shift')) {
            return;
        }

        setState({ ...state, [anchor]: open });
    };
    
    return (
        <div style={{ height: "100%" }}>
            {['bottom'].map((anchor) => (
                <React.Fragment key={anchor}>
                    <div className="expand-search-container">
                        <h1>Search OCAD U</h1>
                        <form onSubmit={(e) => handleSearchSubmit(e, anchor)}>
                            <input 
                                className="expand-search-box" 
                                placeholder="Ask your question here" 
                                value={searchValue}
                                onChange={handleSearchChange}
                            /> 
                            <button 
                                type="submit" 
                                className="expand-search-btn"
                                aria-expanded={state[anchor]} 
                                aria-haspopup="true" 
                                aria-controls={anchor}
                            >
                                →
                            </button>
                        </form>
                    </div>
                    <div className="pattern">
                    </div>

                    <Drawer 
                        className="expand-search-drawer" 
                        id={anchor} 
                        PaperProps={{ 
                            component: 'nav',
                            style: { backgroundColor: '#000000' } 
                        }} 
                        anchor={anchor}
                        open={state[anchor]}
                        onClose={toggleDrawer(anchor, false)}
                    >
                        <iframe 
                            name="expand-search-iframe" 
                            id="expand-search-iframe" 
                            title="search results" 
                            src={iframeUrl} 
                            style={{ border: 0, width: '100%' }} 
                        />
                        <Button 
                            onClick={toggleDrawer(anchor, false)} 
                            aria-expanded={state[anchor]} 
                            aria-haspopup="true" 
                            aria-controls={anchor} 
                            id="expand-search-button-close" 
                            className={classes.margin}
                            style={{ color: '#ffffff', backgroundColor: '#1AB0F6' }}
                        >
                            Close
                        </Button>
                    </Drawer>
                </React.Fragment>
            ))}
        </div>
    );
}
