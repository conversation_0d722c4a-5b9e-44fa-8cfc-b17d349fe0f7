/* global stuff */


@import url("https://use.typekit.net/thk7oyq.css");

.eee-dashboard-card *  {
font-family: "Gotham", Arial, sans-serif !important;
transition: all 0.15s ease-out;
letter-spacing: 0px !important;
}

:root {
    --ocad-blue: #086CF9;
    --ocad-light-blue: #1AB0F6;
    --ocad-purple: #A014EC;
    --ocad-orange: bro##F26323;
    --ocad-red: #F81423;
    --ocad-green: #3CAB49;
    --ocad-yellow: #F7FE4F;
  }


/* super search */

.super-search {
background: #000;
width: 100vw;
padding: 20px 60px 20px 60px;
background-color: var(--ocad-yellow);
margin-top: -1px;
}


.super-search .expand-search-box {
width: 80%;
border: 1px solid #fff;
padding: 10px;
border-radius: 6px 0px 0px 6px;
background: #fff;
z-index: 10;
font-size: 1.0rem;
display: inline-block;
}

.super-search .expand-search-btn {
width: 20%;
display: inline-block;
font-size: 1.5rem;
border: 0px;
padding: 0px 0px 0px 0px;
border-radius: 0px 6px 6px 0px;
background-color: #000;
color: #fff;
text-align: center;
min-height: 0px;
z-index: 10;
border: 1px solid #000;
text-decoration: none;
line-height: 38.5px;
position: relative;
top: 3px;
}


.super-search .expand-search-btn:hover {
color: #000;
background-color: var(--ocad-yellow);
border: 1px solid #000;
}

.super-search .expand-search-btn:hover::after {
color: #000;
}

@media (max-width: 768px) {
.super-search {
padding: 23px 24px 16px 24px;
}
}



/* search card */

.expand-search-container {
    width: 100%;
    height: 100%;
    background-image: url('https://live-ocad-dpl.pantheonsite.io/sites/default/files/myOCADU/images/wave.svg');
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: 50% 100%;
    padding: 20px;
    background-color: #000;
    }
    
    .expand-search-container input {
    width: 80%;
    border: 1px solid #fff;
    padding: 10px;
    border-radius: 6px 0px 0px 6px;
    background: #fff;
    z-index: 10;
    font-size: 1.0rem;
    display: inline-block;
    }
    
    .expand-search-container .expand-search-btn {
    width: 20%;
    display: inline-block;
    font-size: 1.5rem;
    border: 0px;
    padding: 5px 0px 15px 0px;
    border-radius: 0px 6px 6px 0px;
    background-color: var(--ocad-light-blue);
    color: #fff;
    text-align: center;
    min-height: 0px;
    z-index: 10;
    border: 1px solid var(--ocad-light-blue);
    text-decoration: none;
    line-height: 23.5px;
    position: relative;
    top: -2px;
    }
    
    .expand-search-container .expand-search-btn:hover {
    color: #fff;
    background: #000;
    border: 1px solid #fff;
    }
    
    .expand-search-container h1 {
    margin-top: 15%;
    font-size: 1.6rem;
    text-align: center;
    font-weight: 800;
    color: #fff;
    }

















    #expand-search-button {
      position: absolute;
      bottom: 0px;
      right: 0px;
      background-color: #2f6af0;
      border: 1px solid #2f6af0;
      border-radius: 6px 0px 6px 0px;
  }
  
  #expand-search-button:hover {
      color: #2f6af0;
      background-color: #fff;
      border-color: #2f6af0;
  }
  
  .expand-search-drawer nav {
   padding: 20px 10px 10px 10px;
   display: flex;
  }
  
  .expand-search-drawer iframe {
  height: 100%;
  height: 100%;
  display: flex;
  align-items: stretch;
  }
  
  #expand-search-button-close {
      position: absolute;
      bottom: 0px;
      right: 0px;
      background-color: #2f6af0;
      border: 1px solid #2f6af0;
      border-radius: 6px 0px 0px 0px;
  }
  
  #expand-search-button-close:hover {
      color: #2f6af0;
      background-color: #fff;
      border-color: #2f6af0;
  }
  
  .expand-search-drawer {
  width: 100vw;
  height: 75vh;
  background-color: #000000;
}

.expand-search-drawer nav {
  height: 75%;
  background-color: #000000;
}

.expand-search-drawer .MuiPaper-root {
  background-color: #000000;
}

.expand-search-drawer nav {
  height: 75%;
  }
  
  div[data-extension-name="ContactDirectoryDrawer"] .MuiCardContent-root div div {
  overflow-y: hidden;
  }
  
  div[data-extension-name="ContactDirectoryDrawer"] .MuiCardContent-root div div {
      -ms-overflow-style: none;  /* Internet Explorer 10+ */
      scrollbar-width: none;  /* Firefox */
  }
  div[data-extension-name="ContactDirectoryDrawer"] .MuiCardContent-root div div::-webkit-scrollbar { 
      display: none;  /* Safari and Chrome */
  }
  
  @media(max-width: 880px) {
      .expand-search-drawer {
      width: 100vw !important
      }
  }
  
  @media (min-width: 480px) {
      .expand-search-drawer {
          width: 100vw !important
      }
  }
  @media (min-width: 880px) {
      .expand-search-drawer {
          width: 100vw !important
      }
  }

  @media(max-width:650px) {
.expand-search-drawer nav {
    height: 95%;
}

}