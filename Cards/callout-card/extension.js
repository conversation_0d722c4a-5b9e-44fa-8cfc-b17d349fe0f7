module.exports = {
    name: 'Callout<PERSON><PERSON>',
    publisher: '<PERSON>',
    group: 'OCAD University',
    cards: [{
        type: 'CalloutCard2',
        source: './src/cards/CalloutCard.jsx',
        title: 'Callout Card',
        displayCardType: 'Callout Card',
        description: 'Multi-use card to promote a single idea, iniative, or program. Has multiple layout options.',
        template: {
            icon: 'megaphone',
            title: 'Callout Card',
            description: 'Multi-use card to promote a single idea, iniative, or program. Has multiple layout options.'
        },
        customConfiguration: {
            source: './src/cards/CalloutCardConfig.jsx'
        }
    }],
    page: {
        source: './src/page/router.jsx'
    }
};