{"parser": "@babel/eslint-parser", "extends": ["eslint:recommended", "plugin:import/recommended", "plugin:jsx-a11y/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parserOptions": {"babelOptions": {"presets": ["@babel/preset-react"]}, "ecmaFeatures": {"experimentalObjectRestSpread": true, "jsx": true}, "requireConfigFile": false, "sourceType": "module"}, "settings": {"import/resolver": {"node": {"extensions": [".js", ".jsx"]}}, "react": {"version": "detect"}, "linkComponents": ["Hyperlink", {"name": "Link", "linkAttribute": "to"}]}, "env": {"browser": true, "node": true, "es6": true}}