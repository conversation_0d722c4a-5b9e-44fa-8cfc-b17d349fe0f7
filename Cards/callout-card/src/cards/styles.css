/* Radio button fixes */

.MuiFormGroup-root[aria-labelledby="callout-templates"] svg[data-testid="RadioButtonUncheckedIcon"] {
stroke-width: 10px;
width: 25px !important;
height: 25px !important;
}

.MuiFormGroup-root[aria-labelledby="callout-templates"] svg[data-testid="RadioButtonCheckedIcon"] {
border: 1px solid #d2d2d2;
width: 25px !important;
height: 25px !important;
}

input[name="primaryColor"] + span svg[data-testid="RadioButtonUncheckedIcon"] {
stroke-width: 10px;
width: 25px !important;
height: 25px !important;
}

input[name="primaryColor"] + span svg[data-testid="RadioButtonCheckedIcon"] {
border: 1px solid #d2d2d2;
width: 25px !important;
height: 25px !important;
}

input[name="color"] + span svg[data-testid="RadioButtonUncheckedIcon"] {
stroke-width: 10px;
width: 25px !important;
height: 25px !important;
}

input[name="color"] + span svg[data-testid="RadioButtonCheckedIcon"] {
border: 1px solid #d2d2d2;
width: 25px !important;
height: 25px !important;
}
    

/* Template 1 */
#callout-card {
width: 100%;
height: 100%;
margin: 0px;
}

#callout-card .background-template {
width: 100%;
height: 100%;
margin: 0px;
padding: 40px 20px 20px 20px;
background-position: 50% 50;
background-size: cover;
box-shadow: inset 0px 0px 100px 70px #000;
}

#callout-card .background-template h2 {
text-align: center;
line-height: 28px;
color: #fff;
margin-bottom: 20px;
}

#callout-card .background-template p {
text-align: center;
font-size: 16px;
color: #fff;
line-height: 20px;

}


#callout-card .background-template a {
width: calc(100% - 40px);
background-color: #fff;
color: #000;
position: absolute;
bottom: 10px;
left: 20px;
}

#callout-card .background-template a:hover {
    background-color: var(--ocad-light-blue) !important;
color: #000;
}

#callout-card-config tbody  svg {
width: calc(25% - 40px);
margin: 0px 20px 0px 20px;
}

.MuiFormGroup-root[aria-labelledby="callout-templates"] label {
width: calc(25% - 40px);
margin: 0px 20px 0px 20px;
}



/* Template 2 */
#callout-card .icon-template {
    padding: 20px;
}

    #callout-card .icon-template svg {
    width: 100px;
    height: 100px;
    margin-left: calc(50% - 50px);
    margin-bottom: 20px;
    }
    
    #callout-card .icon-template h2 {
    text-align: center;
    line-height: 28px;
    color: #000;
    margin-bottom: 20px;
    }
    
    #callout-card .icon-template p {
    text-align: center;
    font-size: 15px;
    color: #000;
    line-height: 20px;
    }
    
    
    #callout-card .icon-template a {
    width: calc(100% - 40px);
    background-color: #000;
    color: #fff;
    position: absolute;
    bottom: 10px;
    left: 20px;
    }
    
    #callout-card .icon-template a:hover {
    background-color: var(--ocad-light-blue) !important;
    color: #fff;
    }

/* Template 3 */
#callout-card .color-block-template {
width: 100%;
height: 100%;
padding: 20px;
}

#callout-card .color-block-template svg {
width: 100px;
height: 100px;
margin-left: calc(50% - 50px);
margin-bottom: 30px;
margin-top: 40px;
}

#callout-card .color-block-template h3 {
text-align: center;
line-height: 25px;
margin-bottom: 20px;
font-size: 20px;
}

#callout-card .color-block-template:hover {
opacity: 0.75;
cursor: pointer;
}

#callout-card a {   
    text-decoration: none;
}



/* Template 4 */

#callout-card .image-template {
width: 100%;
height: 100%;
background-size: cover;
background-position: center;
}

#callout-card .image-template:hover {
opacity: 0.75;
cursor: pointer;
}


