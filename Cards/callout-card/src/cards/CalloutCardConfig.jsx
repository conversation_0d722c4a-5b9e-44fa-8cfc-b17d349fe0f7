import React from "react";
import PropTypes from "prop-types";
import {
  TextField,
  Table,
  TableBody,
  Typography,
  FormControl,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormLabel
} from "@ellucian/react-design-system/core";

const CalloutCardConfig = (props) => {
  const {
    cardControl: { setCustomConfiguration, setIsCustomConfigurationValid },
    cardInfo: {
      configuration: { customConfiguration }
    }
  } = props;

  const client = customConfiguration ? customConfiguration.client : undefined;
  const [links, setLinks] = React.useState(
    client ? client.links : [{ title: "", text: "", cta: "", link: "", icon: "star", color: "#000000", primarycolor: "#000000", image: "https://www.ocadu.ca/sites/default/files/myOCADU/images/Student%20Diversity%20Census%20Blurred.jpg"}]
  );

  // Initialize template value from customConfiguration if it exists, otherwise default to 'template1'
  const [value, setValue] = React.useState(
    client && client.template ? client.template : 'template1'
  );

  // Initialize bgColor from customConfiguration if it exists
  const [bgColor, setBgColor] = React.useState(
    client && client.backgroundColor ? client.backgroundColor : '#ffffff'
  );

  // Initialize bgColor from customConfiguration if it exists
  const [primaryColor, setPrimaryColor] = React.useState(
    client && client.primaryColor ? client.primaryColor : '#000000'
  );

  const handleChange = (i, e) => {
    const newLinks = [...links];
    newLinks[i][e.target.name] = e.target.value;
    setLinks(newLinks);

    setCustomConfiguration({
      customConfiguration: {
        client: {
          template: value,
          links: newLinks,
          backgroundColor: bgColor,
          primaryColor: primaryColor
        }
      }
    });
  };

  const handleTemplateChange = (event) => {
    setValue(event.target.value);
  };
  const handleBackgroundColorChange = (event) => {
    const newColor = event.target.value;
    setBgColor(newColor);
    
    setCustomConfiguration({
      customConfiguration: {
        client: {
          template: value,
          links: links,
          backgroundColor: newColor
        }
      }
    });
  };

  const handlePrimaryColorChange = (event) => {
    const primColor = event.target.value;
    setPrimaryColor(primColor);
    
    setCustomConfiguration({
      customConfiguration: {
        client: {
          template: value,
          links: links,
          backgroundColor: bgColor,  
          primaryColor: primColor
        }
      }
    });
  };

  const handleBlur = (e) => {
    setIsCustomConfigurationValid(e.target.value !== "");
  };

  const renderTemplateFields = (element, index) => {
    switch(value) {
      case 'template1': // Background Template
        return (
          <>
            <TextField
              label="Title"
              margin="normal"
              name="title"
              onBlur={handleBlur}
              onChange={(e) => handleChange(index, e)}
              value={element.title}
              maxCharacters={140}
            /><br />
            <TextField
              label="Text"
              margin="normal"
              name="text"
              onBlur={handleBlur}
              onChange={(e) => handleChange(index, e)}
              value={element.text}
              maxCharacters={140}
            /><br />
            <TextField
              label="CTA Text"
              margin="normal"
              name="cta"
              onBlur={handleBlur}
              onChange={(e) => handleChange(index, e)}
              value={element.cta}
              maxCharacters={32}
            /><br />
            <TextField
              label="Link URL"
              margin="normal"
              name="link"
              onBlur={handleBlur}
              onChange={(e) => handleChange(index, e)}
              value={element.link}
            /><br />
            <TextField
              label="Image URL"
              margin="normal"
              name="image"
              onBlur={handleBlur}
              onChange={(e) => handleChange(index, e)}
              value={element.image}
            />
          </>
        );

      case 'template2': // Icon Template
        return (
          <>
            <TextField
              label="Text"
              margin="normal"
              name="text"
              onBlur={handleBlur}
              onChange={(e) => handleChange(index, e)}
              value={element.text}
              maxCharacters={110}
            /><br />
            <TextField
              label="Icon"
              margin="normal"
              name="icon"
              onBlur={handleBlur}
              onChange={(e) => handleChange(index, e)}
              value={element.icon}
            /><br />
            <FormControl>
              <FormLabel id="primary-radio-buttons-group">Primary Color</FormLabel>
              <RadioGroup
                row
                aria-labelledby="primary-radio-buttons-group"
                name="primaryColor"
                value={primaryColor}
                onChange={handlePrimaryColorChange}
              >
                <FormControlLabel value="#1AB0F6" control={<Radio />} label="Light Blue" />
                <FormControlLabel value="#086CF9" control={<Radio />} label="Blue" />
                <FormControlLabel value="#F81423" control={<Radio />} label="Red" />
                <FormControlLabel value="#F26323" control={<Radio />} label="Orange" />
                <FormControlLabel value="#3CAB49" control={<Radio />} label="Green" />
                <FormControlLabel value="#A014EC" control={<Radio />} label="Purple" />
                <FormControlLabel value="#F7FE4F" control={<Radio />} label="Yellow" />
                <FormControlLabel value="#000000" control={<Radio />} label="Black" />
                <FormControlLabel value="#ffffff" control={<Radio />} label="White" />
              </RadioGroup>
            </FormControl>
            <TextField
              label="CTA"
              margin="normal"
              name="cta"
              onBlur={handleBlur}
              onChange={(e) => handleChange(index, e)}
              value={element.cta}
              maxCharacters={32}
            /><br />
            <TextField
              label="Link URL"
              margin="normal"
              name="link"
              onBlur={handleBlur}
              onChange={(e) => handleChange(index, e)}
              value={element.link}
            />
          </>
        );

      case 'template3': // Colour Block Template
        return (
          <>
            <TextField
              label="Title"
              margin="normal"
              name="title"
              onBlur={handleBlur}
              onChange={(e) => handleChange(index, e)}
              value={element.title}
              maxCharacters={110}
            /><br />
            <TextField
              label="Icon"
              margin="normal"
              name="icon"
              onBlur={handleBlur}
              onChange={(e) => handleChange(index, e)}
              value={element.icon}
            />
            <br /><br />

            <FormControl>
              <FormLabel id="backgroundcolor-radio-buttons-group">Background Color</FormLabel>
              <RadioGroup
                row
                aria-labelledby="backgroundcolor-radio-buttons-group"
                name="color"
                value={bgColor}
                onChange={handleBackgroundColorChange}
              >
                <FormControlLabel value="#1AB0F6" control={<Radio />} label="Light Blue" />
                <FormControlLabel value="#086CF9" control={<Radio />} label="Blue" />
                <FormControlLabel value="#F81423" control={<Radio />} label="Red" />
                <FormControlLabel value="#F26323" control={<Radio />} label="Orange" />
                <FormControlLabel value="#3CAB49" control={<Radio />} label="Green" />
                <FormControlLabel value="#A014EC" control={<Radio />} label="Purple" />
                <FormControlLabel value="#F7FE4F" control={<Radio />} label="Yellow" />
                <FormControlLabel value="#000000" control={<Radio />} label="Black" />
                <FormControlLabel value="#ffffff" control={<Radio />} label="White" />
              </RadioGroup>
            </FormControl>
            <br />
            <FormControl>
              <FormLabel id="primary-radio-buttons-group">Primary Color</FormLabel>
              <RadioGroup
                row
                aria-labelledby="primary-radio-buttons-group"
                name="primaryColor"
                value={primaryColor}
                onChange={handlePrimaryColorChange}
              >
                <FormControlLabel value="#1AB0F6" control={<Radio />} label="Light Blue" />
                <FormControlLabel value="#086CF9" control={<Radio />} label="Blue" />
                <FormControlLabel value="#F81423" control={<Radio />} label="Red" />
                <FormControlLabel value="#F26323" control={<Radio />} label="Orange" />
                <FormControlLabel value="#3CAB49" control={<Radio />} label="Green" />
                <FormControlLabel value="#A014EC" control={<Radio />} label="Purple" />
                <FormControlLabel value="#F7FE4F" control={<Radio />} label="Yellow" />
                <FormControlLabel value="#000000" control={<Radio />} label="Black" />
                <FormControlLabel value="#ffffff" control={<Radio />} label="White" />
              </RadioGroup>
            </FormControl>
            <TextField
              label="Link URL"
              margin="normal"
              name="link"
              onBlur={handleBlur}
              onChange={(e) => handleChange(index, e)}
              value={element.link}
            />
          </>
        );

      case 'template4': // Image Template
        return (
          <>
            <TextField
              label="Image URL"
              margin="normal"
              name="image"
              onBlur={handleBlur}
              onChange={(e) => handleChange(index, e)}
              value={element.image}
            /><br />
            <TextField
              label="Link URL"
              margin="normal"
              name="link"
              onBlur={handleBlur}
              onChange={(e) => handleChange(index, e)}
              value={element.link}
            />
          </>
        );

      default:
        return null;
    }
  };

  return (
    <div>
      <Typography variant="h3">To find the names of icons available, please visit the <a href="https://fontawesome.com/search?ip=classic&s=solid&o=r"> Font Awesome Library</a></Typography>
      <Table id="callout-card-config">
        <TableBody>
        <svg width="199" height="123" viewBox="0 0 199 123" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="199" height="123" rx="6" fill="#B7B7B7"/>
                <rect x="34" y="21" width="131" height="6" fill="white"/>
                <rect x="34" y="64" width="131" height="4" fill="#D7D7D7"/>
                <rect x="39" y="36" width="121" height="6" fill="white"/>
                <rect x="39" y="75" width="121" height="4" fill="#D7D7D7"/>
                <rect x="9" y="98" width="182" height="18" rx="3" fill="white"/>
              </svg>

              <svg width="199" height="123" viewBox="0 0 199 123" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="199" height="123" rx="6" fill="#B7B7B7"/>
                <rect x="34" y="67" width="131" height="4" fill="#D7D7D7"/>
                <rect x="39" y="78" width="121" height="4" fill="#D7D7D7"/>
                <rect x="9" y="98" width="182" height="18" rx="3" fill="white"/>
                <circle cx="100" cy="34" r="20" fill="#D7D7D7"/>
              </svg>

              <svg width="199" height="123" viewBox="0 0 199 123" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="199" height="123" rx="6" fill="#B7B7B7"/>
                <circle cx="99" cy="41" r="20" fill="#D7D7D7"/>
                <rect x="33" y="82" width="131" height="6" fill="white"/>
                <rect x="38" y="96" width="121" height="6" fill="white"/>
              </svg>

              <svg width="199" height="123" viewBox="0 0 199 123" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clipPath="url(#clip0_278_14)">
                <rect width="199" height="123" rx="6" fill="#B7B7B7"/>
                <circle cx="33" cy="30" r="15" fill="#D7D7D7"/>
                <path d="M61.5873 59.75L1.35878 99.112C0.511001 99.666 0 100.61 0 101.623V120C0 121.657 1.34315 123 3 123H196.5C198.157 123 199.5 121.657 199.5 120V82.1153C199.5 81.3958 199.241 80.7003 198.771 80.1556L157.35 32.1442C156.311 30.9406 154.514 30.756 153.253 31.7234L90.9592 79.4975C89.821 80.3704 88.2242 80.3155 87.1486 79.3665L65.2134 60.0118C64.2012 59.1187 62.7172 59.0116 61.5873 59.75Z" fill="#D7D7D7"/>
                </g>
                <defs>
                <clipPath id="clip0_278_14">
                <rect width="199" height="123" rx="6" fill="white"/>
                </clipPath>
                </defs>
              </svg>
          <FormControl>
            <RadioGroup
              row
              aria-labelledby="callout-templates"
              name="controlled-radio-buttons-group"
              value={value}
              onChange={handleTemplateChange}
            >
              
              



              <FormControlLabel value="template1" control={<Radio />} label="Background Template" />
              <FormControlLabel value="template2" control={<Radio />} label="Icon Template" />
              <FormControlLabel value="template3" control={<Radio />} label="Colour Block Template" />
              <FormControlLabel value="template4" control={<Radio />} label="Image Template" />
            </RadioGroup>
          </FormControl>

          {Array.isArray(links) ? links.map((element, index) => (
            <React.Fragment key={index}>
              {renderTemplateFields(element, index)}
            </React.Fragment>
          )) : (
            <React.Fragment>
              {renderTemplateFields({
                title: "",
                text: "",
                cta: "",
                link: "",
                icon: "",
                image: ""
              }, 0)}
            </React.Fragment>
          )}
        </TableBody>
      </Table>
    </div>
  );
};

CalloutCardConfig.propTypes = {
  cardControl: PropTypes.object,
  cardInfo: PropTypes.object
};

export default CalloutCardConfig;
