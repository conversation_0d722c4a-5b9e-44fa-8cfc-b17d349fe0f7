{"name": "CalloutCard", "version": "1.0.0", "description": "Multi-use card to promote a single idea, iniative, or program. Has multiple layout options.", "main": "index.js", "license": "UNLICENSED", "scripts": {"lint": "npx eslint --ext .jsx,.js src", "build-dev": "webpack --progress --mode development --env verbose", "build-prod": "webpack --progress --mode production --env verbose", "deploy-dev": "webpack --progress --mode development --env verbose --env upload --env forceUpload", "deploy-prod": "webpack --progress --mode production --env verbose --env upload", "watch-and-upload": "webpack --hot --watch --mode development --env verbose --env upload --env forceUpload", "start": "webpack serve --mode development --env verbose --env liveReload"}, "dependencies": {"@ellucian/ds-icons": "https://cdn.elluciancloud.com/assets/EDS2/7.15.0/umd/path_design_system_icons.tgz", "@ellucian/experience-extension-utils": "https://cdn.elluciancloud.com/assets/SDK/utils/1.0.0/ellucian-experience-extension-utils-1.0.0.tgz", "@ellucian/react-design-system": "https://cdn.elluciancloud.com/assets/EDS2/7.15.0/umd/path_design_system.tgz", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "prop-types": "15.7.2", "react": "17.0.2", "react-dom": "17.0.2", "react-fontawesome": "^1.7.1", "react-intl": "5.12.5", "react-markdown": "^8.0.6", "react-router-dom": "5.2.0"}, "devDependencies": {"@babel/eslint-parser": "7.17.0", "@babel/plugin-transform-runtime": "7.12.1", "@babel/preset-env": "7.12.1", "@babel/preset-react": "7.12.1", "@ellucian/experience-extension": "https://cdn.elluciancloud.com/assets/SDK/7.13.0/ellucian-experience-extension-7.13.0.tgz", "babel-plugin-rewire": "1.2.0", "cross-env": "7.0.2", "dotenv": "8.2.0", "eslint": "8.8.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-jsx-a11y": "6.5.1", "eslint-plugin-react": "7.28.0", "eslint-plugin-react-hooks": "4.6.0", "webpack": "5.76.0", "webpack-cli": "4.10.0", "webpack-dev-server": "4.7.4"}}