<style>


@import url("https://use.typekit.net/thk7oyq.css");

	#accordion-card dt {
	padding: 20px 20px;
	margin-bottom: 0;
	border-radius: 0px;
	display: block;
	}
	
	#accordion-card a {
	text-decoration: none !important;
	color: #fff;
	}
	
	#accordion-card dt svg {
	flex: none !important;
	margin-right: 15px;
	height: 1.5rem;
	}
	
	#accordion-card dt {
	background-color: #1DB0F6;
	justify-content: left;
	font-family: "Gotham", Arial, sans-serif;
	text-transform: none;
	letter-spacing: 0px;
	z-index: 9;
	border-radius: 6px;
	margin-bottom: 10px;
	position: relative;
	}
	#accordion-card a:nth-child(1n) dt {
	background-color: #1DB0F6 !important;
	}

	#accordion-card a:nth-child(2n) dt{
	background-color: #3CAB49 !important;
	}
	#accordion-card a:nth-child(3n) dt {
	background-color: #A014EC !important;
	}
	#accordion-card a:nth-child(4n) dt {
	background-color: #F7FE4F !important;
	color: #000;
	}
	#accordion-card a:nth-child(5n) dt {
	background-color: #F81423 !important;
	}
	#accordion-card a:nth-child(6n) dt {
	background-color: #086CF9 !important;
	}
	#accordion-card a:nth-child(7n) dt {
	background-color: #F26323 !important;
	}
	#accordion-card .accordion-body a {
	color: #000;
	text-decoration: none;
	border-bottom: 1px solid #000;
	cursor: pointer;
	}
	#accordion-card .accordion-body a:hover {
	border-bottom: 3px solid #000;
	}
	#accordion-card .accordion-body ul {
	padding-left: 1rem;
	}
	#accordion-card .accordion-button:focus {
	box-shadow: none !important;
	}
	#accordion-card button:nth-child(2n){
	background-color: #3CAB49 !important;
	}
	#accordion-card button:nth-child(3n){
	background-color: #A014EC !important;
	}
	#accordion-card button:nth-child(4n){
	background-color: #F7FE4F !important;
	color: #000;
	}
	#accordion-card button:nth-child(5n){
	background-color: #F81423 !important;
	}
	#accordion-card button:nth-child(6n){
	background-color: #086CF9 !important;
	}
	#accordion-card button:nth-child(7n){
	background-color: #F26323 !important;
	}

.accordion dt .accordion-icon::before {
  content: "∨";
}

.accordion dt[aria-expanded="true"] .accordion-icon::before {
  content: "∧";
}

.accordion dt .accordion-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: scale(2, 1);
}

.accordion-button {
background: transparent;
border: 0px;
color: #fff;
font-family: Gotham;
font-size: 18px;
}

.accordion-button:hover {
background: transparent;
border: 0px;
color: #fff;
}
	
	
	dd {
	margin-inline-start: 0px;
	padding: 0px 20px 20px 20px;
	margin-bottom: 20px;

	font-family: "Gotham", Arial, sans-serif;
	}
	
	
	#accordion-card dd a {
	text-decoration: none !important;
	color: #000;
	border-bottom: 1px solid #000;
	display: inline-block;
	margin-bottom: 10px;
	padding-bottom: 2px;
	}
	
	#accordion-card dd a:hover {
	border-bottom: 3px solid #000;
	padding-bottom: 0px;
	}
	
	
.accordion a dt::after {
flex-shrink: 0;
width: 20px;
height: 20px;
margin-left: auto;
position: absolute;
top: 20px;
right: 20px;
content: "∨";
background-repeat: no-repeat;
background-size: cover;
transform: scale(2, 1);
}
	
#button-grid {
clear: both;
padding-bottom: 0px;
width: 100%;
padding-top: 5px;
display: flex;
flex-direction: row;
flex-wrap: nowrap;
justify-content: normal;
align-items: normal;
align-content: normal;
}

.important-contact::after, #button-grid::after, #accordion-card::after {
content: "\00a0"; /* Older browser do not support empty content */
visibility: hidden;
display: block;
height: 0;
clear: both;
}


#button-grid button {
display: block;
flex-grow: 0;
flex-shrink: 1;
flex-basis: auto;
align-self: auto;
order: 0;
width: 100%;
cursor: pointer;
margin-bottom: 0px;
background: #000;
text-align: left;
align-items: start;
padding: 15px;
border: 0px;
line-height: 19px;
font-size: 13px;
align-items: start;
justify-content: left;
border-radius: 6px;
font-family: Gotham, Helvetica Neue, Helvetica, -apple-system, BlinkMacSystemFont, Open Sans, Source Sans Pro, "Droid Serif", Arial, Segoe UI, sans-serif;
letter-spacing: 0px;
font-weight: 700;
text-transform: none;
color: #fff;
max-width: 33.33%;
}

#button-grid button:hover {
border: 0px;
}

#button-grid button:nth-of-type(1) {
margin-right: 10px;
}

#button-grid button:nth-of-type(2) {
margin-right: 10px;
}

#button-grid button.wide-true {
flex: 1 0 calc(100% - 20px);
}

#button-grid button svg {
flex: none;
}

#button-grid button:nth-child(1n):hover {
background: #5da955;
}

#button-grid button:nth-child(2n):hover {
background: #53adf0;
color: #000;
}

#button-grid button:nth-child(2n):hover img {
filter: invert(1);
}

#button-grid button:nth-child(3n):hover {
background: #9325e3;
}

#button-grid button:nth-child(4n):hover {
background: #2f6af0;
}

#button-grid button:nth-child(5n):hover {
background: #e43732;
}

#button-grid button:nth-child(6n):hover {
background: #e16c38;
}

#button-grid button:nth-child(7n):hover {
background: #f3e65b;
color: #000;
}

#button-grid button:nth-child(7n):hover img {
filter: invert(1);
}


.important-contact {
width: 100%;
border-radius: 6px;
font-family: Gotham, Helvetica Neue, Helvetica, -apple-system, BlinkMacSystemFont, Open Sans, Source Sans Pro, "Droid Serif", Arial, Segoe UI, sans-serif;
letter-spacing: 0px;
font-weight: 700;
line-height: 19px;
font-size: 15px;
background: #dedede;
margin-bottom: 10px;
}

.important-contact a {
color: #fff;
text-decoration: none;
}

.important-contact strong {
padding: 20px;
display: block;
width: calc(50% - 40px);
float: left;
font-weight: 700;
}

.important-contact span {
display: block;

width: calc(50% - 40px);
float: right;
color: #fff;
border-radius: 0px 6px 6px 0px;
background: #F81423;
padding: 20px;
} 

.important-contact span:hover {
background: #A014EC;
}

	
</style>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script>
jQuery(function($) {
  // Initialize accordions - ensure all are closed by default
  $('.accordion dt').attr('aria-expanded', 'false');
  $('.accordion dd').attr('aria-hidden', 'true').hide();
  
  // Convert links to buttons for better accessibility
  $('.accordion a').each(function(index) {
    const $link = $(this);
    const $dt = $link.find('dt');
    const dtContent = $dt.html();
    const headingId = 'accordion-heading-' + index;
    const panelId = 'accordion-panel-' + index;
    
    // Set up proper ARIA attributes
    $dt.attr({
      'id': headingId,
      'aria-controls': panelId,
      'tabindex': '0',
      'role': 'button'
    });
    
    // Replace link with proper HTML structure
    $dt.html(`
      <button class="accordion-button" aria-expanded="false">
        ${dtContent}
        <span class="accordion-icon" aria-hidden="true"></span>
      </button>
    `);
    
    // Set up panel attributes
    $link.next('dd').attr({
      'id': panelId,
      'aria-labelledby': headingId,
      'role': 'region'
    });
  });
  
  // Handle click events
  $('.accordion dt').on('click keypress', function(e) {
    // Only trigger on click or Enter/Space key
    if (e.type === 'keypress' && e.which !== 13 && e.which !== 32) {
      return;
    }
    
    e.preventDefault();
    
    const $dt = $(this);
    const $button = $dt.find('.accordion-button');
    const $panel = $('#' + $dt.attr('aria-controls'));
    const isExpanded = $dt.attr('aria-expanded') === 'true';
    
    // Close all panels
    $('.accordion dt').attr('aria-expanded', 'false');
    $('.accordion dt .accordion-button').attr('aria-expanded', 'false');
    $('.accordion dd').attr('aria-hidden', 'true').slideUp();
    
    // If the clicked panel wasn't expanded, open it
    if (!isExpanded) {
      $dt.attr('aria-expanded', 'true');
      $button.attr('aria-expanded', 'true');
      $panel.attr('aria-hidden', 'false').slideDown();
    }
  });

   if (/Mobi|Android/i.test(navigator.userAgent)) {
   		$(".desktop").hide();
   		$(".mobile").show();
	}
	else {
		$(".desktop").show();
    	$(".mobile").hide();
	}

  // Handle button clicks for navigation
  $('#button-grid button[data-href]').on('click', function(e) {
    e.preventDefault();
    const href = $(this).attr('data-href');
    if (href) {
      window.open(href, '_blank');
    }
  });
});

</script>


<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>


<div class="important-contact desktop"><strong>Security assistance</strong> <a href="https://teams.microsoft.com/l/call/0/0?users=<EMAIL>.com0" target="_top"><span>************ ext. 511</span></a></div>

<div class="important-contact mobile"><strong>Security assistance</strong> <a href="tel:************,511#" target="_top"><span>************ ext. 511</span></a></div>
<!--- <div class="important-contact" style="margin-bottom: 10px; text-align: center; padding: 20px 0px;"><FontAwesomeIcon icon={item.icon || "bars"} />If there is a true emergency, call 911</a></div> -->




<div id="button-grid">

<button data-href="https://map.ocadu.ca/?id=1998#!ce/62382?ct/60994,60995,60996,60997,60998,61000,61001,61002,61003,61004,61005,61006,61138,61139,61140,61141,61142,61143,61144,61146,61147,61148,61149,61150,61151,61152,61153,61154,61155,61156,61157,61159,61160,61161,61162,61163,61165,61166,61167,61168,61169,61170,61174,61175,61176,61177,61178,61179,61181,61182,61183,61184,61185,61186,61187,61188,61189,61190,61191,61192,61193,61194,61195,61196,61197,61198,61199,61200?s/">Campus Map</button>
<button data-href="https://ocadu.topdesk.net/tas/public/ssp/content/detail/service?unid=e741dd499609444396af43b94a297382">Emergency Procedures</button>
<button data-href="https://ocadu.topdesk.net/tas/public/ssp/content/detail/knowledgeitem?unid=0fecb7d0d3ca487cafcf8183d37937ea">Walk Safe</button>

</div>
 
<div id="accordion-card">
  <dl class="accordion">
    <div>
      <dt id="support-resources-heading" aria-expanded="false" aria-controls="support-resources-panel" tabindex="0" role="button">
        <button class="accordion-button" aria-expanded="false">
          Support Resources
          <span class="accordion-icon" aria-hidden="true"></span>
        </button>
      </dt>
      <dd id="support-resources-panel" aria-labelledby="support-resources-heading" role="region" aria-hidden="true">
        <a href="https://ocadu.topdesk.net/tas/public/ssp/2ee2b724-6270-4a7f-b84a-3ba6c4c85783" target="_blank">Contact Safety & Security Services</a><br>
		<a href="https://ocadu.topdesk.net/tas/public/ssp/400bed39-70b9-4667-aed9-315673609877" target="_blank">See a Doctor or Counsellor</a><br>
		<a href="https://ocadu.topdesk.net/tas/public/ssp/content/detail/service?unid=27f2ee31891d4f5ca7106c313cd959c8&from=9514b10f-1f16-4f82-a913-f0b79c867558" target="_blank">Access Sexual Violence Support</a><br>
		<a href="https://ocadu.topdesk.net/tas/public/ssp/a5447cd7-b6e4-45b4-a667-bd6ca3584585" target="_blank">Request Accessibility Services</a><br>
		<a href="mailto:<EMAIL>" target="_blank">Report a Cybersecurity Incident</a><br>
      </dd>
    </div>
  </dl>
</div>

