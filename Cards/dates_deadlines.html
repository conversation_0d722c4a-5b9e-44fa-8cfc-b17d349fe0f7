<style>

body, html {
padding: 0px 0px 0px 0px;
margin: 0px;
height: 100%;
overflow-x: hidden;
}

.slider {
width: 100%;
padding-bottom: 15vh;
}

.date-card {
font-family: "Gotham", sans-serif;
margin: 10px 0px 0px 4vw;
width: calc(100vw - 8vw) !important;
border-radius: 6px;
background: #ffffff;
border: 1px solid #a3a3a3;
}

.date-card .info-panel {

padding: 10px;
}

.date-card:hover {
background: #d9d9d9;
border-color: #d9d9d9; 
}

.slider a {
color: #000 !important;
text-decoration: none;
}

.slider a:hover {
color: #888 !important;
}

.date-card h3 {
font-size: 4.5vw;
margin: 0px 0px 0px 0px;
display: block;
}

.date-card .date {
font-size: 4.5vw !important;
margin-bottom: 0px;
font-weight: 600;
display: block;
width: calc(100vw - 8vw - 20px);
border-radius: 6px 6px 0px 0px;
background: #000;
color: #fff;
padding: 10px;
margin: -10px 0px 10px -10px;
}


.date-card .date span.date-to {
display: inline;
}

.date-card .title {
font-weight: 600;
padding: 0px 20px 0px 0px;
}
  
.date-card .title p {
font-size: 3.75vw;
font-weight: 400;
padding: 0px 20px 0px 0px;
}

.card-container {
position: relative;
}

.info-panel {
width: 100%;
}

.cta-banner {
position: fixed;
bottom: 0;
left: 0;
z-index: 99;
background: #000;
color: #fff;
height: 25px;
width: 100%;
font-family: "Gotham", sans-serif;
font-size: 15px;
padding: 12px 40px 6px 10px;
}

.cta-banner:hover {
color: #000;
background-color: #fff;
}

</style>


<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,700;1,400&display=swap" rel="stylesheet">


<div class="card-container">
  <div class="slider">

  </div>
  <div class="controls"></div>
  
  <a href="https://www.ocadu.ca/student-services/office-registrar/dates-and-deadlines" target="_blank">
    <div class="cta-banner">See all Dates and Deadlines ⟶</div>
  </a>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-migrate/3.5.2/jquery-migrate.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.9.0/slick.min.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.7.8/handlebars.min.js" ></script>




<script id="each-template" type="text/x-handlebars-template">
{{#each this}}
    <div class="date-card">
      <div class="info-panel">
        <span class="date index-{{@index}}">{{{this.field_date}}}</span>
        <h3 class="title">{{this.title}}{{{this.field_summary}}}</h3>
        
      </div>
    
    </div>

{{/each}}
</script>


<script>
  
var template;
 

$.ajax({
url: 'https://www.ocadu.ca/api/dates_deadlines',
type: "GET",
dataType: "json",
success: function (data) {   
  template = Handlebars.compile($("#each-template").html());
  $('.slider').html(template(data));
  $( ".date-card .date").each(function( index ) {
   $(this).html($(this).html().replace("-","<span class='date-to'>to</span>"));
  });
 
  
}
});
</script>

