<style>



* {
	font-family: Gotham, Helvetica Neue, Helvetica, -apple-system, BlinkMacSystemFont, Open Sans, Source Sans Pro, "Droid Serif", Arial, Segoe UI, sans-serif;
	transition: all 0.15s ease-out;
}
body, html {
padding: 0px;
margin: 0px;
height: 100%;
overflow-x: hidden;
}

.card-container {
padding-bottom: 25vw;
}


.date-card {
font-family: "Roboto", sans-serif;
margin: 10px 0px 0px 4vw;
width: calc(100vw - 8vw) !important;
background: #ffffff;
border-bottom: 1px solid #000;
padding: 2vw 0px;
}

.date-card .info-panel {
padding: 10px;
}

.card-container a {
color: #000 !important;
text-decoration: none;
}

.card-container .info-panel:hover {
color: #888 !important;
}

.date-card .date {
font-size: 3.75vw;
margin: 0px 0px 0px 0px;
display: block;
font-weight: 500;
}

.date-card .title {
font-size: 4.5vw !important;
margin-bottom: 0px;
font-weight: 600;
display: block;
width: calc(100vw - 8vw - 20px);
padding: 10px;
margin: -10px 0px 10px -10px;
}


.date-card .title {
font-weight: 600;
}
  
.date-card .title p {
font-size: 3.75vw;
font-weight: 400;
padding: 0px 20px 0px 0px;
}

.card-container {
position: relative;
}

.info-panel {
width: 100%;
}

.wrapper-footer-button {
    position: fixed;
    bottom: 0px;
    left: 0%;
    width: 100%;
    padding: 4vw 20px 4vw 20px;
    filter: drop-shadow(0px 4px 14px #000000);
    background: #fff;
    box-sizing: border-box;
}

  .footer-button {
  display: block;
    border-radius: 6px;
    width: 100%;
    background: #000;
    color: #fff;
    font-size: 4vw;
    padding: 15px;
    text-decoration: none;
    box-sizing: border-box;
    text-align: center;
  }
  
    .footer-button:hover {
    background: #1AB0F6;
    }

  .footer-button img {
  float: left;
  width: 6vw;
  height: 6vw;
  }

</style>


<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,700;1,400&display=swap" rel="stylesheet">


<div class="card-container">


</div>


<div class="wrapper-footer-button">
<a href="https://ocadu-csm.symplicity.com/" class="footer-button" target="_blank">
<img src="https://live-ocad-dpl.pantheonsite.io/sites/default/files/myOCADU/icons/selfservice.png" alt="service icon">
Explore More Opportunities</a>
</div>
	

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.7.8/handlebars.min.js" ></script>




<script id="each-template" type="text/x-handlebars-template">
{{#each this}}
    <div class="date-card">
     <a href="{{view_node}}" target="_blank"> 
     	<div class="info-panel">
      	 <span class="title index-{{@index}}">{{{this.title}}}</span>
       	 <h3 class="date">{{{this.field_news_date}}}</h3>	 
      	</div>
      </a>
    
    </div>

{{/each}}
</script>


<script>
  
var template;
 

$.ajax({
	url: 'https://www.ocadu.ca/api/cfs',
	type: "GET",
	dataType: "json",
	success: function (data) {   
		template = Handlebars.compile($("#each-template").html());
		$('.card-container').html(template(data));
		
		}
	
});
</script>

