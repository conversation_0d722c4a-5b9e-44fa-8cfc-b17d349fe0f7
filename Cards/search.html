<style>


* {
	font-family: "Gotham", sans-serif;
	transition: all 0.15s ease-out;

}

body,
html {	
    background: #000;
	padding: 0px;
	margin: 0px;
	overflow-x: hidden;
}

  body, html {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
}
body::-webkit-scrollbar, html::-webkit-scrollbar { 
    display: none;  /* Safari and Chrome */
}


body {
background-image: url('https://live-ocad-dpl.pantheonsite.io/sites/default/files/myOCADU/images/wave.svg');
background-repeat: no-repeat;
background-size: 100% auto;
background-position: 50% 100%;
height: 100%;
}

svg g {
width: 100%;
}

.search-container {
	width: 92vw;
	padding: 10px 4vw 0px 4vw;
	position: fixed;
	left: 0;
	top: 40%;
	transform: translateY(-70%);
	z-index: 999;
	background: transparent
}

.search-box {
	width: 80%;
height: 40px;
	font-size: clamp(20px, 6vw, 24px);

	border: 1px solid #fff;
	padding: 2vw;
	border-radius: 6px 0px 0px 6px;
	margin-bottom: 4vw;
	background: #fff;
	z-index: 10;
	float: left;
	font-size: 5vw;
}

.search-btn {
	width: 14%;
height: 38px;
	float: left;
	font-size: 5vw;
	border: 0px;
	padding: 0vw 2vw 0vw 2vw;
	border-radius: 0px 6px 6px 0px;
	margin-bottom: 2vw;
	background-color: #1ab0f6;
	color: #fff;
	text-align: center;
	min-height: 0px;
	z-index: 10;
	border: 1px solid #1ab0f6;
	text-decoration: none;
	line-height: 38px;
}


.search-btn:hover {
	color: #fff;
	background: #000;
	border: 1px solid #fff;
}

.search-btn:hover::after {
	color: #000;
}

.pattern svg {
	width: calc(100vw - 2px);
	position: fixed;
	left: 1px;
	bottom: -2.5vw;
	z-index: 9;
    border-width: 0px 1px 0px 1px;
}

h1 {
	color: #fff;
	font-size: 7vw;
	text-align: center;
}

</style>

 

<div class="search-container">
	<h1>Search OCAD U</h1>
	<form><input class="search-box" placeholder="Pay a fee, How to borrow a camera...">
	<div class="search-btn" href="" target="_blank">→</div></form>

</div>
<div class="pattern">

</div>


  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

<script>


 $('.search-btn').on('click', function(e) {
  
  let a= document.createElement('a');
  let response = 'https://search.ocadu.ca?search=' + $(".search-box").val();
  	a.target= '_blank';
	a.href= response;
	a.click();
});

$('.search-box').keypress(function (e) {
   var keyCode = e.keyCode ? e.keyCode : e.which;       
   if (keyCode == 13) 
   {
      e.preventDefault();
  let a= document.createElement('a');
  let response = 'https://search.ocadu.ca?search=' + $(".search-box").val();
	a.target= '_blank';
	a.href= response;
	a.click();
      return false; 
   }
});


  </script>