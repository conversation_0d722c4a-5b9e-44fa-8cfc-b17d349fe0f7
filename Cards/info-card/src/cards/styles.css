/* global stuff */


@import url("https://use.typekit.net/thk7oyq.css");

.eee-dashboard-card *  {
font-family: "Gotham", Arial, sans-serif !important;
transition: all 0.15s ease-out;
letter-spacing: 0px !important;
}

:root {
    --ocad-blue: #086CF9;
    --ocad-light-blue: #1AB0F6;
    --ocad-purple: #A014EC;
    --ocad-orange: bro##F26323;
    --ocad-red: #F81423;
    --ocad-green: #3CAB49;
    --ocad-yellow: #F7FE4F;
  }

.info-card {
  padding: 16px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.info-card h2 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
  font-size: 1.5rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.info-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-row {
  display: flex;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.info-label {
  flex: 0 0 120px;
  font-weight: 600;
  color: #555;
}

.info-value {
  flex: 1;
  word-break: break-word;
}

.info-footer {
  margin-top: 16px;
  text-align: center;
  color: #888;
  font-size: 0.8rem;
}

@media (max-width: 480px) {
  .info-row {
    flex-direction: column;
    gap: 4px;
  }
  
  .info-label {
    flex: none;
  }
}

.debug-section {
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.debug-section h3 {
  margin-top: 0;
  font-size: 1rem;
  color: #555;
}

.debug-info pre {
  background-color: #eee;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 0.8rem;
  white-space: pre-wrap;
}

