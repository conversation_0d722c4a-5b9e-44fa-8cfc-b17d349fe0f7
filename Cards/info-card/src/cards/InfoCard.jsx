import React from 'react';
import { 
  useUserInfo
} from '@ellucian/experience-extension-utils';
import "./styles.css";
import Barcode from 'react-barcode';


export default function InfoCard() {
  const userInfo = useUserInfo();
  
  // Format user data for display
  const userData = [
    { label: 'User ID', value: userInfo?.id || 'Not available' },
    { label: 'School ID', value: userInfo?.schoolId || userInfo?.bannerId || userInfo?.studentId || 'Not available' },
    { label: 'Display Name', value: userInfo?.displayName || 'Not available' },
    { label: 'First Name', value: userInfo?.firstName || userInfo?.givenName || 'Not available' },
    { label: 'Last Name', value: userInfo?.lastName || userInfo?.surname || 'Not available' },
    { label: 'Pronouns', value: userInfo?.pronouns || 'Not available' },
    { label: 'Email', value: userInfo?.mail || userInfo?.email || 'Not available' }
  ];

  return (
    <>
      <div className="info-card">
        <div className="info-container">
          {userData.map((item, index) => (
            <div className="info-row" key={index}>
              <div className="info-label">{item.label}:</div>
              <div className="info-value">{item.value}</div>
            </div>
          ))}
          <Barcode value="A23390100315496A" format="codabar" />
        </div>
      </div>
    </>
  );
}
